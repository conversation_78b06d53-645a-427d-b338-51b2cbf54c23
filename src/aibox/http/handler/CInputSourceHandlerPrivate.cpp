#include "CInputSourceHandlerPrivate.h"
// #include "../../Header.h"
#include "../../ServiceUnits.h"
#include "../../util/StringUtility.h"
#include <../../log/Log.h>
// #include <QtDebug>
// #include <QtDebug>
// #include <QSqlError>
// #include <QSqlRecord>
// #include <QFile>
#include <algorithm>
#include <numeric>
#include <string>
#include <vector>

#include "CParam.h"
// #include "CDBHelper.h"
#include "CDirManager.h"
#include "CGlobal.h"
#include "CHttpTools.h"
// #include "LogMacro.h"
// #include "json.hpp"
// #include <QDebug>
#include "../../db/DBExports.h"
#include "../../util/StringFormat.h"
#include "CHttpCommon.h"
#include <algorithm>
#include <ctime>
#include <iostream>
// #include <QDateTime>
using namespace LogComm;

static constexpr uint16_t _push_port = 18868;

void CInputSourceHandlerPrivate::getList(const httplib::Request &req, httplib::Response &res) {

    if (!CHttpTools::checkSession(req, res))
        return;

    // 具体数据

    DB::PreparedStatement *stmt = DB::LogonDatabasePool.GetPreparedStatement(AI_BOX_CAMERA_LIST);

    DB::PreparedQueryResult result = DB::LogonDatabasePool.Query(stmt);
    std::vector<std::string> bindValues;
    std::string str = "[]";
    if (result) {
        Json::Value jsonList;
         do {
            Json::Value json;
            DB::Field *field = result->Fetch();
            json["id"] = field[0].GetInt32();
            json["name"] = field[1].GetString();
            json["url"] = field[2].GetString();
            json["start"] = field[3].GetInt32();
           
            jsonList.append(json);
        }while (result->NextRow());

       
        if (jsonList.size() > 0)
        {
            str = jsonList.toStyledString();
        }
        
        std::cout << "getList ------ str:" << str << std::endl;
        
    }
    res.set_content(CHttpTools::jsonObj(true, str), HttpContentType::json);
    res.status = httplib::StatusCode::OK_200;
}

void CInputSourceHandlerPrivate::query(const httplib::Request &req, httplib::Response &res) {
    if (!CHttpTools::checkSession(req, res))
        return;

    Req_Read_File(type);      // �??几页(如果从�??一页开始算，需�??-1)
    Req_Read_File(page);      // �??几页(如果从�??一页开始算，需�??-1)
    Req_Read_File(pageCount); // 一页�?�少�??

    if (!type.valid() || !page.valid() || !pageCount.valid()) {
        res.set_content(CHttpTools::json(false, u8"缺少参数"), HttpContentType::json);
        return;
    }

    const int32_t vpage = std::stoi(page.value());
    const int32_t vpageCount = std::stoi(pageCount.value());

    if (1) {
        DB::PreparedStatement *stmt = DB::LogonDatabasePool.GetPreparedStatement(AI_BOX_CAMERA_COUNT);

        DB::PreparedQueryResult result = DB::LogonDatabasePool.Query(stmt);
        int totalCount = 0;
        if (result) {
            DB::Field *field = result->Fetch();
            totalCount = field[0].GetInt32();
        }

        const int totalPage = std::ceil(totalCount * 1.0 / vpageCount);

        if (totalCount < 1) {
            res.set_content(CHttpTools::jsonObj(true, "[]"), HttpContentType::json);
            return;
        }

        DB::PreparedStatement *stmtList = DB::LogonDatabasePool.GetPreparedStatement(AI_BOX_CAMERA_INFO_LIST);
        stmtList->SetString(0, type.value());
        int start = vpage > 0 ? vpage * vpageCount : 0;
        stmtList->SetInt32(1, start);
        stmtList->SetInt32(2, vpageCount);
        DB::PreparedQueryResult resultList = DB::LogonDatabasePool.Query(stmtList);
        if (resultList) {
            Json::Value cameraList;
            do {
                Json::Value json;
                DB::Field *field = resultList->Fetch();
                int id = field[0].GetInt32();
                json["id"] = field[0].GetInt32();
                json["sid"] = field[1].GetString();
                json["brand"] = field[2].GetString();
                json["name"] = field[3].GetString();
                json["ip"] = field[4].GetString();
                json["user"] = field[5].GetString();
                json["pwd"] = field[6].GetString();
                json["protocol"] = field[7].GetString();
                json["stream_url"] = field[8].GetString();
                json["format"] = field[9].GetString();
                json["tcp"] = field[10].GetInt32();
                json["record"] = field[11].GetInt32();
                json["start"] = field[12].GetInt32();
                json["online"] = field[13].GetInt32();
                json["push_url"] = field[14].GetString();

                DB::PreparedStatement *algorithmList =
                    DB::LogonDatabasePool.GetPreparedStatement(AI_BOX_ALGORITHM_INFO);
                algorithmList->SetInt32(0, id);
                
                DB::PreparedQueryResult resultAlgorithm = DB::LogonDatabasePool.Query(algorithmList);
                std::string errJson("");
                if (resultAlgorithm) {
                    Json::Value algJson;
                    do
                    {
                        DB::Field *field = resultAlgorithm->Fetch();
                        int i = 0;
                        algJson["alg_id"] = field[i++].GetInt32();
                        algJson["cycle"] = field[i++].GetInt32();
                        algJson["dp"] = field[i++].GetInt32();
                        algJson["dp_id"] = field[i++].GetInt32();
                        algJson["interval"] = field[i++].GetInt32();

                        algJson["hasTimes"] = field[i++].GetInt32();
                        algJson["level"] = field[i++].GetInt32();
                        algJson["people_count"] = field[i++].GetInt32();
                        try
                        {
                            auto strThrs = field[i++].GetString();
                            errJson.clear();
                            Json::Value thrsJson = Util::stringToJson(strThrs, errJson);
                             if(errJson.empty()){
                                 algJson["thrs"] = thrsJson;
                             }else{
                                 algJson["thrs"] = Json::arrayValue;
                             }

                             auto strVehicletype = field[i++].GetString();
                             errJson.clear();
                             Json::Value vehicletypeJson = Util::stringToJson(strVehicletype, errJson);
                             if (errJson.empty()) {
                                 algJson["vehicletype"] = vehicletypeJson;
                             } else {
                                algJson["vehicletype"] = Json::arrayValue;
                             }

                             auto strVehs = field[i++].GetString();
                             errJson.clear();
                             Json::Value vehsJson = Util::stringToJson(strVehs, errJson);
                             if (errJson.empty()) {
                                 algJson["vehs"] = vehsJson;
                             } else {
                                 algJson["vehs"] = Json::arrayValue;
                             }

                             auto strLines = field[i++].GetString();
                             errJson.clear();
                             Json::Value linesJson = Util::stringToJson(strLines, errJson);
                             if (errJson.empty()) {
                                 algJson["lines"] = linesJson;
                             } else {
                                 algJson["lines"] = Json::arrayValue;
                             }

                             auto strPolygons = field[i++].GetString();
                             errJson.clear();
                             Json::Value polygonsJson = Util::stringToJson(strPolygons, errJson);
                             if (errJson.empty()) {
                                 algJson["polygons"] = polygonsJson;
                             } else {
                                 algJson["polygons"] = Json::arrayValue;
                             }

                            
                        }
                        catch(const std::exception& e)
                        {
                            std::cerr << e.what() << '\n';
                        }
                        
                        


                        algJson["width"] = field[i++].GetInt32();
                        algJson["height"] = field[i++].GetInt32();
                        algJson["wb"] = field[i++].GetInt32();

                        json["alg_conf"].append(algJson);
                    } while (resultAlgorithm->NextRow());

                    
                }

                // std::string str = Util::jsonToString(json);
                cameraList["list"].append(json);
                std::cout << "CInputSourceHandlerPrivate::query ret:11111 " << std::endl;
            } while (resultList->NextRow());
            cameraList["page"] = vpage;
            cameraList["totalPage"] = totalPage;
            cameraList["count"] = totalCount;
            std::string ret = cameraList.toStyledString();
            //  Util::vectorToString<std::string>(cameraList, ret, ",");
            std::cout << "CInputSourceHandlerPrivate::query ret: " << ret << std::endl;
            res.set_content(CHttpTools::jsonObj(true, ret), HttpContentType::json);
            res.status = httplib::StatusCode::OK_200;
        }
    }
}

void CInputSourceHandlerPrivate::total(const httplib::Request &req, httplib::Response &res) {
    if (!CHttpTools::checkSession(req, res))
        return;

    Req_Read_File(type);

    if (!type.valid()) {
        res.set_content(CHttpTools::json(false, u8"缺少参数"), HttpContentType::json);
        return;
    }

    DB::PreparedStatement *stmt = DB::LogonDatabasePool.GetPreparedStatement(AI_BOX_CAMERA_COUNT_BY_TYPE);
    stmt->SetString(0, type.value());
    int totalCount = 0;
    DB::PreparedQueryResult result = DB::LogonDatabasePool.Query(stmt);
    if (result) {
        DB::Field *field = result->Fetch();
        totalCount = field[0].GetInt32();
        LOG_INFO("InputSource.total", "totalCount: %d  ", totalCount);
    }

    DB::PreparedStatement *stmt2 = DB::LogonDatabasePool.GetPreparedStatement(AI_BOX_CAMERA_COUNT_BY_START);
    stmt2->SetString(0, type.value());
    

    int totalCount2 = 0;
    DB::PreparedQueryResult query2 = DB::LogonDatabasePool.Query(stmt2);
    if (query2) {
        DB::Field *field = query2->Fetch();
        totalCount2 = field[0].GetInt32();
        LOG_INFO("InputSource.total", "totalCount2: %d  ", totalCount2);
    }

    Json::Value json;
    json["total"] = totalCount;
    json["start"] = totalCount2;
    json["offline"] = totalCount - totalCount2;
    std::string str = json.toStyledString();

    // Json::Value json;
    // json["total"] = 0;
    // json["start"] = 0;
    // json["offline"] = 0;
    // std::string str = json.toStyledString();
    //                       // LOG_INFO("InputSource.total", "json: %s  ", str.c_str());
    //                       std::cout
    //                   << "CInputSourceHandlerPrivate::total json: " << str << std::endl;
    res.set_content(CHttpTools::jsonObj(true, str), HttpContentType::json);
    res.status = httplib::StatusCode::OK_200;
}

void CInputSourceHandlerPrivate::enable(const httplib::Request &req, httplib::Response &res) {
    if (!CHttpTools::checkSession(req, res))
        return;

    Req_Read_File(id);
    Req_Read_File(start);

    if (!id.valid() || !start.valid()) {
        res.set_content(CHttpTools::json(false, u8"缺少参数"), HttpContentType::json);
        return;
    }

    const int _id = std::stoi(id.value());
    const int _start = std::stoi(start.value());

    DB::PreparedStatement *stmt = DB::LogonDatabasePool.GetPreparedStatement(AI_BOX_CAMERA_START);
    stmt->SetInt32(0, _start);
    stmt->SetInt32(1, _id);
    DB::PreparedQueryResult query = DB::LogonDatabasePool.Query(stmt);

    std::cout << "------------id:" << id << " start:" << start << std::endl;

    SrvUnitsMgr->PostControlRequest(AiBox::UDC_MDM_MB_CAMERA, nullptr, 0);

    // if(query) {
    //     if(query.value().numRowsAffected() > 0) {
    //         if(_start == 1) {
    //             QList<QVariant> bindValues2;
    //             const QString txt2("select stream_url from input_source_table
    //             where id=?"); bindValues2 << _id; auto future2 =
    //             XDBHelper.execSql(txt2, bindValues2); auto query2  =
    //             future2.get();

    //             if(query2.value().next()) {
    //                 QString url = query2.value().value(0).toString();
    //                 std::cout<<"--CInputSourceHandlerPrivate::enable----"<<std::endl;
    //                 CHttpTools::notifyAddSource(_id, url);
    //             }

    //         } else {
    //             CHttpTools::notifyRemoveSource(_id);
    //         }
    //         res.set_content(CHttpTools::json(true, u8"处理成功"),
    //         HttpContentType::json);
    //     }
    //     else
    //         res.set_content(CHttpTools::json(false, u8"处理失败"),
    //         HttpContentType::json);
    res.set_content(CHttpTools::json(true, u8"处理成功"), HttpContentType::json);
    res.status = httplib::StatusCode::OK_200;
    }

    void CInputSourceHandlerPrivate::add(const httplib::Request &req, httplib::Response &res) {
        if (!CHttpTools::checkSession(req, res))
            return;

        Req_Read_File(type);       // 1/camera  2/video
        Req_Read_File(sid);        // 摄像头id
        Req_Read_File(brand);      // 品牌�??
        Req_Read_File(name);       // 名称
        Req_Read_File(ip);         // ip
        Req_Read_File(user);       // 账号
        Req_Read_File(pwd);        // 密码
        Req_Read_File(protocol);   // trsp  rtmp  http
        Req_Read_File(stream_url); // 流地址
        Req_Read_File(format);     // h264 hevc
        Req_Read_File(alg_conf);   // 算法json {}
        Req_Read_File(tcp);        // �??�??TCP 推流 0/1
        Req_Read_File(record);     // �??否录�??  0/1
        Req_Read_File(start);      // �??否启�??  1
        Req_Read_File(online);     // �??否在�??  1
        std::cout << "alg_conf-----------------" << alg_conf.value() << std::endl;
        if (alg_conf.valid()) {
            // setAlgConfigFileContent(id, alg_conf.value());
            Json::Value algJson;
            Json::CharReaderBuilder builder;
            std::istringstream iss(alg_conf.value());
            std::string errs;
            if (!Json::parseFromStream(builder, iss, &algJson, &errs)) {
                res.set_content(CHttpTools::json(false, u8"算法json格式错�??"), HttpContentType::json);
                return;
            }
            for (auto alg : algJson) {
                if (!alg.isMember("alg_id") || !alg.isMember("cycle") || !alg.isMember("dp") ||
                    !alg.isMember("dp_id") || !alg.isMember("interval") || !alg.isMember("hasTimes") ||
                    !alg.isMember("level") || !alg.isMember("people_count") || !alg.isMember("thrs") ||
                    !alg.isMember("vehicletype") || !alg.isMember("vehs") || !alg.isMember("lines") ||
                    !alg.isMember("polygons") || !alg.isMember("width") || !alg.isMember("height") ||
                    !alg.isMember("wb")) {
                    res.set_content(CHttpTools::json(false, u8"算法缺少参数"), HttpContentType::json);
                    return;
                }
            }
        }

         std::string push_url =
            fmt::format("http://{}:{}/live/irstream/?.live.flv", XGlobal->getHttpHost().c_str(), _push_port);
        // QString push_url = QString("http://%1:%2/live/irstream/?.live.flv")
        //                        .arg(QString::fromStdString( XGlobal->getHttpHost()
        //                        )) .arg(_push_port);
         std::cout << "getHttpHost :" << XGlobal->getHttpHost().c_str() << "  port:" << _push_port
                   << " push_url:" << push_url << std::endl;
         if (1) {

             DB::PreparedStatement *stmt = DB::LogonDatabasePool.GetPreparedStatement(AI_BOX_CAMERA_ADD);
             stmt->SetString(0, type.value());
             stmt->SetString(1, sid.value());
             stmt->SetString(2, brand.value());
             stmt->SetString(3, name.value());
             stmt->SetString(4, ip.value());
             stmt->SetString(5, user.value());
             stmt->SetString(6, pwd.value());
             stmt->SetString(7, protocol.value());
             stmt->SetString(8, stream_url.value());
             stmt->SetString(9, push_url);
             stmt->SetString(10, format.value());
             stmt->SetInt32(11, std::stoi(tcp.value()));
             stmt->SetInt32(12, std::stoi(record.value()));
             stmt->SetInt32(13, std::stoi(start.value()));
             stmt->SetInt32(14, std::stoi(online.value()));
             DB::LogonDatabasePool.Query(stmt);

             DB::PreparedStatement *stmtId = DB::LogonDatabasePool.GetPreparedStatement(AI_BOX_CAMERA_ADD_ID);

             DB::PreparedQueryResult queryId = DB::LogonDatabasePool.Query(stmtId);
             int id = 0;
             if (queryId) {
                 DB::Field *field = queryId->Fetch();
                 id = field[0].GetInt32();
             }

             std::cout << "id:" << id << std::endl;
             push_url =
                fmt::format("http://{}:{}/live/irstream/{}.live.flv", XGlobal->getHttpHost().c_str(), _push_port, id);

            if (alg_conf.valid()) {
                // setAlgConfigFileContent(id, alg_conf.value());
                Json::Value algJson;
                Json::CharReaderBuilder builder;
                std::istringstream iss(alg_conf.value());
                std::string errs;
                if (!Json::parseFromStream(builder, iss, &algJson, &errs)) {
                    res.set_content(CHttpTools::json(false, u8"算法json格式错�??"), HttpContentType::json);
                    return;
                }
                for (auto alg : algJson) {
                    int alg_id = 0;
                    if (alg["alg_id"].isInt()) {
                        alg_id = alg["alg_id"].asInt();
                    }

                    int cycle = 0;
                    if (alg["cycle"].isInt()) {
                        cycle = alg["cycle"].asInt();
                    }
                    int dp = 0;
                    if (alg["dp"].isInt()) {
                        dp = alg["dp"].asInt();
                    }
                    int dp_id = 0;
                    if (alg["dp_id"].isInt()) {
                        dp_id = alg["dp_id"].asInt();
                    }
                    int interval = 0;
                    if (alg["interval"].isInt()) {
                        interval = alg["interval"].asInt();
                    }
                    int hasTimes = 0;
                    if (alg["hasTimes"].isInt()) {
                        hasTimes = alg["hasTimes"].asInt();
                    }
                    int level = 0;
                    if (alg["level"].isInt()) {
                        level = alg["level"].asInt();
                    }

                    int people_count = 0;
                    if (alg["people_count"].isInt()) {
                        people_count = alg["people_count"].asInt();
                    }

                    std::string thrs = alg["thrs"].toStyledString();

                    std::string vehicletype = alg["vehicletype"].toStyledString();

                    std::string vehs = alg["vehs"].toStyledString();

                    std::string lines = alg["lines"].toStyledString();

                    std::string polygons = alg["polygons"].toStyledString();

                    int width = 0;
                    if (alg["width"].isInt()) {
                        width = alg["width"].asInt();
                    }
                    int height = 0;
                    if (alg["height"].isInt()) {
                        height = alg["height"].asInt();
                    }
                    int wb = 0;
                    if (alg["wb"].isInt()) {
                        wb = alg["wb"].asInt();
                    }

                    DB::PreparedStatement *stmtAlg =
                        DB::LogonDatabasePool.GetPreparedStatement(AI_BOX_CAMERA_ALG_CONF_ADD);
                    stmtAlg->SetInt32(0, id);
                    stmtAlg->SetInt32(1, alg_id);
                    stmtAlg->SetInt32(2, cycle);
                    stmtAlg->SetInt32(3, dp);
                    stmtAlg->SetInt32(4, dp_id);
                    stmtAlg->SetInt32(5, interval);
                    stmtAlg->SetInt32(6, hasTimes);
                    stmtAlg->SetInt32(7, level);
                    stmtAlg->SetInt32(8, people_count);
                    stmtAlg->SetString(9, thrs);
                    stmtAlg->SetString(10, vehicletype);
                    stmtAlg->SetString(11, vehs);
                    stmtAlg->SetString(12, lines);
                    stmtAlg->SetString(13, polygons);
                    stmtAlg->SetInt32(14, width);
                    stmtAlg->SetInt32(15, height);
                    stmtAlg->SetInt32(16, wb);
                    DB::LogonDatabasePool.Query(stmtAlg);
                    insertLines(id, alg_id, name, lines);
                }
            }

            DB::PreparedStatement *stmt2 = DB::LogonDatabasePool.GetPreparedStatement(AI_BOX_CAMERA_PUSH_URL);
            stmt2->SetString(0, push_url);
            stmt2->SetInt32(1, id);
            DB::LogonDatabasePool.Query(stmt2);
            

            // TODO: 这里需要通知算法

            res.set_content(CHttpTools::json(true, u8"添加成功"), HttpContentType::json);
            res.status = httplib::StatusCode::OK_200;
         }
}

void CInputSourceHandlerPrivate::batchDelete(const httplib::Request &req, httplib::Response &res) {
    if (!CHttpTools::checkSession(req, res))
        return;

    Req_Read_File(ids);

    if (!ids.valid()) {
        res.set_content(CHttpTools::json(false, u8"缺少参数"), HttpContentType::json);
        return;
    }
    const std::string sIds = ids.value();
    Json::Value json;
    Json::CharReaderBuilder builder;
    std::istringstream iss(sIds);
    std::string errs;
    if (!Json::parseFromStream(builder, iss, &json, &errs)) {
        res.set_content(CHttpTools::json(false, u8"算法json格式错�??"), HttpContentType::json);
        return;
    }

    std::string s = "";
    for (auto item : json) {
        s += item.asString() + ",";
    }

    // 去掉最后一�??逗号
    s.erase(s.end() - 1);

    DB::PreparedStatement *stmt = DB::LogonDatabasePool.GetPreparedStatement(AI_BOX_CAMERA_BATCH_REMOVE);
    stmt->SetString(0, s);
    DB::PreparedQueryResult query = DB::LogonDatabasePool.Query(stmt);
    if (!query) {
        res.set_content(CHttpTools::json(false, u8"批量删除摄像头失�??"), HttpContentType::json);
        return;
    }

    for (auto item : json) {
        // TODO 通知算法
        auto nId = item.asInt();
        DB::PreparedStatement *stmt2 = DB::LogonDatabasePool.GetPreparedStatement(AI_BOX_CAMERA_ALG_REMOVE);
        stmt2->SetInt32(0, nId);
        DB::PreparedQueryResult query2 = DB::LogonDatabasePool.Query(stmt2);
        if (!query2) {
            res.set_content(CHttpTools::json(false, u8"批量删除算法失败"), HttpContentType::json);
            return;
        }
    }

    res.set_content(CHttpTools::json(true, u8"批量删除成功"), HttpContentType::json);

    res.status = httplib::StatusCode::OK_200;
    SrvUnitsMgr->PostControlRequest(AiBox::UDC_MDM_MB_CAMERA, nullptr, 0);
}

void CInputSourceHandlerPrivate::remove(const httplib::Request &req, httplib::Response &res) {
    if (!CHttpTools::checkSession(req, res))
        return;

    Req_Read_File(id);

    if (!id.valid()) {
        res.set_content(CHttpTools::json(false, u8"缺少参数"), HttpContentType::json);
        return;
    }

    const int nId = std::stoi(id.value());
    DB::PreparedStatement *stmt = DB::LogonDatabasePool.GetPreparedStatement(AI_BOX_CAMERA_REMOVE);

    stmt->SetInt32(0, nId);
    DB::LogonDatabasePool.Query(stmt);
    

    // TODO: 这里需要通知算法

    DB::PreparedStatement *stmt2 = DB::LogonDatabasePool.GetPreparedStatement(AI_BOX_CAMERA_ALG_REMOVE);
    stmt2->SetInt32(0, nId);
    DB::LogonDatabasePool.Query(stmt2);
    
    res.set_content(CHttpTools::json(true, u8"删除成功"), HttpContentType::json);
    res.status = httplib::StatusCode::OK_200;

    SrvUnitsMgr->PostControlRequest(AiBox::UDC_MDM_MB_CAMERA, nullptr, 0);
}

void CInputSourceHandlerPrivate::edit(const httplib::Request &req, httplib::Response &res) {
    if (!CHttpTools::checkSession(req, res))
        return;

    Req_Read_File(id);
    Req_Read_File(sid);
    Req_Read_File(brand);
    Req_Read_File(name);
    Req_Read_File(ip);
    Req_Read_File(user);
    Req_Read_File(pwd);
    Req_Read_File(protocol);
    Req_Read_File(stream_url);
    Req_Read_File(format);
    Req_Read_File(alg_conf);
    Req_Read_File(tcp);
    Req_Read_File(record);
    Req_Read_File(start);
    Req_Read_File(online);

    if (alg_conf.valid()) {
        Json::Value algJson;
        Json::CharReaderBuilder builder;
        std::istringstream iss(alg_conf.value());
        std::string errs;
        if (!Json::parseFromStream(builder, iss, &algJson, &errs)) {
            res.set_content(CHttpTools::json(false, u8"算法json格式错�??"), HttpContentType::json);
            return;
        }
        for (auto alg : algJson) {
            if (!alg.isMember("alg_id") || !alg.isMember("cycle") || !alg.isMember("dp") || !alg.isMember("dp_id") ||
                !alg.isMember("interval") || !alg.isMember("hasTimes") || !alg.isMember("level") ||
                !alg.isMember("people_count") || !alg.isMember("thrs") || !alg.isMember("vehicletype") ||
                !alg.isMember("vehs") || !alg.isMember("lines") || !alg.isMember("polygons") ||
                !alg.isMember("width") || !alg.isMember("height") || !alg.isMember("wb")) {
                res.set_content(CHttpTools::json(false, u8"算法缺少参数"), HttpContentType::json);
                return;
            }
        }
    }

    std::vector<std::string> args;
    std::vector<std::string> val_list;
    if (sid.valid()) {

        args.emplace_back(fmt::format("sid = {}", sid.value()));
    }
    if (brand.valid()) {
        args.emplace_back(fmt::format("brand = \"{}\"", brand.value()));
    }
    if (name.valid()) {
        args.emplace_back(fmt::format("name = \"{}\"", name.value()));
    }
    if (ip.valid()) {
        std::string strIP = ip.value();
        std::cout << "1111111111111111 strIP:" << strIP << "|" << std::endl;
        auto strtemp = Util::StringUtility::Trim(strIP) ;
        std::cout << "strtemp: " << strtemp << " strIP:" << strIP << "|" << std::endl;
        args.emplace_back(fmt::format("ip = \"{}\"", strIP));
    }
    if (user.valid()) {
        args.emplace_back(fmt::format("user = \"{}\"", user.value()));
    }
    if (pwd.valid()) {
        args.emplace_back(fmt::format("pwd = \"{}\"", pwd.value()));
    }
    if (protocol.valid()) {
        args.emplace_back(fmt::format("protocol = \"{}\"", protocol.value()));
    }
    if (stream_url.valid()) {
        args.emplace_back(fmt::format("stream_url = \"{}\"", stream_url.value()));
    }
    if (tcp.valid()) {
        args.emplace_back(fmt::format("tcp = {}", tcp.value()));
    }
    if (record.valid()) {
        args.emplace_back(fmt::format("record = {}", record.value()));
    }
    if (start.valid()) {
        args.emplace_back(fmt::format("start = {}", start.value()));
    }
    if (online.valid()) {
        args.emplace_back(fmt::format("online = {}", online.value()));
        val_list.emplace_back(online.value());
    }
    if (format.valid()) {
        args.emplace_back(fmt::format("format = \"{}\"", format.value()));
    }

    if (args.size() < 1) {

        res.set_content(CHttpTools::json(false, u8"缺少参数"), HttpContentType::json);
        res.status = httplib::StatusCode::OK_200;
        return;
    }

    if (1) {
        const int _nId = std::stoi(id.value());
        std::string strAgs = "";
        Util::vectorToString(args, strAgs, ",");
        // update input_source_table set ? where id = ?
        std::string sql = fmt::format("update input_source_table set {} where id = {}", strAgs, _nId);
        DB::QueryResult query =DB::LogonDatabasePool.Query(sql.c_str());

        // DB::PreparedStatement *stmt = DB::LogonDatabasePool.GetPreparedStatement(AI_BOX_CAMERA_EDIT);

        // stmt->SetString(0, strAgs);
        // stmt->SetInt32(1, std::stoi(id.value()));
        // DB::PreparedQueryResult query = DB::LogonDatabasePool.Query(stmt);
        

        {

            // 查出所有�?�摄像头的所有算�??
            std::vector<int> algVec;
            DB::PreparedStatement *stmtAlg = DB::LogonDatabasePool.GetPreparedStatement(AI_BOX_CAMERA_ALG_ID);
            stmtAlg->SetInt32(0, std::stoi(id.value()));
            DB::PreparedQueryResult queryAlg = DB::LogonDatabasePool.Query(stmtAlg);
            if (queryAlg) {
                do{
                    DB::Field *field = queryAlg->Fetch();
                    int alg_id = field[0].GetInt32();
                    algVec.push_back(alg_id);
                } while (queryAlg->NextRow());

            }

            std::vector<int> newAlgVec;
            std::cout << "-----------alg_conf.value()-------------" << alg_conf.value()<<std::endl;
             if (alg_conf.valid()) {
                Json::Value algJson;
                Json::CharReaderBuilder builder;
                std::istringstream iss(alg_conf.value());
                std::string errs;
                if (!Json::parseFromStream(builder, iss, &algJson, &errs)) {
                    res.set_content(CHttpTools::json(false, u8"算法json格式错�??"), HttpContentType::json);
                    return;
                }
                for (auto alg : algJson) {
                    int alg_id = 0;
                    if (alg["alg_id"].isInt()) {
                        alg_id = alg["alg_id"].asInt();
                        newAlgVec.push_back(alg_id);
                    }
                    int cycle = 0;
                    if (alg["cycle"].isInt()) {
                        cycle = alg["cycle"].asInt();
                    }
                    int dp = 0;
                    if (alg["dp"].isInt()) {
                        dp = alg["dp"].asInt();
                    }
                    int dp_id = 0;
                    if (alg["dp_id"].isInt()) {
                        dp_id = alg["dp_id"].asInt();
                    }
                    int interval = 0;
                    if (alg["interval"].isInt()) {
                        interval = alg["interval"].asInt();
                    }
                    int hasTimes = 0;
                    if (alg["hasTimes"].isInt()) {
                        hasTimes = alg["hasTimes"].asInt();
                    }
                    int level = 0;
                    if (alg["level"].isInt()) {
                        level = alg["level"].asInt();
                    }
                    int people_count = 0;
                    if (alg["people_count"].isInt()) {
                        people_count = alg["people_count"].asInt();
                    }
                    std::string thrs = alg["thrs"].toStyledString();
                    std::string vehicletype = alg["vehicletype"].toStyledString();
                    std::string vehs = alg["vehs"].toStyledString();
                    std::string lines = alg["lines"].toStyledString();
                    std::string polygons = alg["polygons"].toStyledString();
                    int width = 0;
                    if (alg["width"].isInt()) {
                        width = alg["width"].asInt();
                    }
                    int height = 0;
                    if (alg["height"].isInt()) {
                        height = alg["height"].asInt();
                    }
                    int wb = 0;
                    if (alg["wb"].isInt()) {
                        wb = alg["wb"].asInt();
                    }

                    auto findIt = std::find(algVec.begin(), algVec.end(), alg_id);
                    if (findIt != algVec.end()) {
                        DB::PreparedStatement *stmtAlg1 =
                            DB::LogonDatabasePool.GetPreparedStatement(AI_BOX_CAMERA_ALG_CONF_UPDATE);
                        stmtAlg1->SetInt32(0, cycle);
                        stmtAlg1->SetInt32(1, dp);
                        stmtAlg1->SetInt32(2, dp_id);
                        stmtAlg1->SetInt32(3, interval);
                        stmtAlg1->SetInt32(4, hasTimes);
                        stmtAlg1->SetInt32(5, level);
                        stmtAlg1->SetInt32(6, people_count);
                        stmtAlg1->SetString(7, thrs);
                        stmtAlg1->SetString(8, vehicletype);
                        stmtAlg1->SetString(9, vehs);
                        stmtAlg1->SetString(10, lines);
                        stmtAlg1->SetString(11, polygons);
                        stmtAlg1->SetInt32(12, width);
                        stmtAlg1->SetInt32(13, height);
                        stmtAlg1->SetInt32(14, wb);
                        stmtAlg1->SetInt32(15, std::stoi(id.value()));
                        stmtAlg1->SetInt32(16, alg_id);
                        DB::PreparedQueryResult queryAlg1 = DB::LogonDatabasePool.Query(stmtAlg1);
                        insertLines(_nId, alg_id, name, lines);
                    } else {

                        // 没有就插�??
                        DB::PreparedStatement *stmtAlg2 =
                            DB::LogonDatabasePool.GetPreparedStatement(AI_BOX_CAMERA_ALG_CONF_ADD);
                        stmtAlg2->SetInt32(0, _nId);
                        stmtAlg2->SetInt32(1, alg_id);
                        stmtAlg2->SetInt32(2, cycle);
                        stmtAlg2->SetInt32(3, dp);
                        stmtAlg2->SetInt32(4, dp_id);
                        stmtAlg2->SetInt32(5, interval);
                        stmtAlg2->SetInt32(6, hasTimes);
                        stmtAlg2->SetInt32(7, level);
                        stmtAlg2->SetInt32(8, people_count);
                        stmtAlg2->SetString(9, thrs);
                        stmtAlg2->SetString(10, vehicletype);
                        stmtAlg2->SetString(11, vehs);
                        stmtAlg2->SetString(12, lines);
                        stmtAlg2->SetString(13, polygons);
                        stmtAlg2->SetInt32(14, width);
                        stmtAlg2->SetInt32(15, height);
                        stmtAlg2->SetInt32(16, wb);

                        DB::PreparedQueryResult queryAlg2 = DB::LogonDatabasePool.Query(stmtAlg2);
                    }
                }

                std::vector<int> removeList;
                for (auto algId : algVec) {
                    auto it = std::find(newAlgVec.begin(), newAlgVec.end(), algId);
                    if (it == newAlgVec.end()) {
                        removeList.emplace_back(algId);
                    }
                }

                if (removeList.size() > 0) {

                    std::string strList = "";
                    Util::vectorToString(removeList, strList, ",");

                    //"delete from camera_alg_conf where `camera_id`= ?  and alg_id in ( ? ) "
                    auto delSql = fmt::format("delete from camera_alg_conf where `camera_id`= {}  and alg_id in ( {} )",
                                              _nId, strList);
                    DB::LogonDatabasePool.Query(delSql.c_str());
                    // DB::PreparedStatement *stmtAlg3 =
                    //     DB::LogonDatabasePool.GetPreparedStatement(AI_BOX_CAMERA_ALG_CONF_DELETE);
                    // stmtAlg3->SetInt32(0, _nId);
                    // stmtAlg3->SetString(1, strList);
                    // DB::PreparedQueryResult queryAlg3 = DB::LogonDatabasePool.Query(stmtAlg3);
                }
            }

            // TODO: 这里需要通知算法
            //  //通知更新
            //  QString txt2 = "select stream_url from input_source_table where
            //  id=?"; QList<QVariant> bindValues2; bindValues2 << _nId; auto
            //  future2 = XDBHelper.execSql(txt2, bindValues2); auto query2 =
            //  future2.get(); if(query2 && query2.value().next()) {
            //      CHttpTools::notifyModifySource(_nId,
            //      query2.value().value(0).toString());
            //  }

            

            res.set_content(CHttpTools::json(true, u8"�??改成�??"), HttpContentType::json);
            SrvUnitsMgr->PostControlRequest(AiBox::UDC_MDM_MB_CAMERA, nullptr, 0);
            res.status = httplib::StatusCode::OK_200;
        }
    }
}

void CInputSourceHandlerPrivate::queryAndNotify() {
    // QString txt = QString("select id,stream_url from input_source_table "
    //                       " where enable=1 and start=1");
    // std::future<std::optional<QSqlQuery>> future = XDBHelper.execSql(txt);
    // std::optional<QSqlQuery> query = future.get();

    // if(query)
    // {
    //     while(query.value().next()) {
    //         const int id = query.value().value(0).toInt();
    //         const QString stream_url = query.value().value(1).toString();
    //         CHttpTools::notifyAddSource(id, stream_url);
    //     }
    // }
}

bool CInputSourceHandlerPrivate::queryParam(int id, std::string &param) {
    bool bRet = false;
    if (id < 0) {
        return bRet;
    }

    return getAlgConfigFileContent(id, param);
}

bool CInputSourceHandlerPrivate::getAlgConfigFileContent(int id, std::string &content) {

    DB::PreparedStatement *stmt = DB::LogonDatabasePool.GetPreparedStatement(AI_BOX_CAMERA_ALG_CONF_LIST);
    stmt->SetInt32(0, id);
    DB::PreparedQueryResult query = DB::LogonDatabasePool.Query(stmt);
    if (!query) {
        content = "[]";
        return false;
    }

    Json::Value algJson;

    do{
        Json::Value jalg;
        DB::Field *field = query->Fetch();
        int camera_id = field[0].GetInt32();
        int alg_id = field[1].GetInt32();
        int cycle = field[2].GetInt32();
        int dp = field[3].GetInt32();
        int dp_id = field[4].GetInt32();
        int interval = field[5].GetInt32();
        int hasTimes = field[6].GetInt32();
        int level = field[7].GetInt32();
        int people_count = field[8].GetInt32();
        std::string thrs = field[9].GetString();
        std::string vehicletype = field[10].GetString();
        std::string vehs = field[11].GetString();
        std::string lines = field[12].GetString();
        std::string polygons = field[13].GetString();
        int width = field[14].GetInt32();
        int height = field[15].GetInt32();
        int wb = field[16].GetInt32();
        jalg["alg_id"] = alg_id;
        jalg["cycle"] = cycle;
        jalg["dp"] = dp;
        jalg["dp_id"] = dp_id;

        jalg["interval"] = interval;
        jalg["hasTimes"] = hasTimes;
        jalg["level"] = level;
        jalg["people_count"] = people_count;

        std::string err("");
        jalg["thrs"] = Util::stringToJson(thrs, err);
        if (!err.empty()) {
            jalg["thrs"] = thrs;
        }
        err = "";
        jalg["vehicletype"] = Util::stringToJson(vehicletype, err);
        if (!err.empty()) {
            jalg["vehicletype"] = vehicletype;
        }
        err = "";
        jalg["vehs"] = Util::stringToJson(vehs, err);
        if (!err.empty()) {
            jalg["vehs"] = vehs;
        }
        err = "";
        jalg["lines"] = Util::stringToJson(lines, err);
        if (!err.empty()) {
            jalg["lines"] = lines;
        }
        err = "";
        jalg["polygons"] = Util::stringToJson(polygons, err);
        if (!err.empty()) {
            jalg["polygons"] = polygons;
        }

        jalg["width"] = width;
        jalg["height"] = height;
        jalg["wb"] = wb;
        algJson.append(jalg);
    } while (query->NextRow());
    

    content = "[]";
    if (!algJson.empty()) {
        content = algJson.toStyledString();
    }

    return true;
}

void CInputSourceHandlerPrivate::setAlgConfigFileContent(int id, const std::string &content) {}

void CInputSourceHandlerPrivate::insertLines(int cameraId, const int &aglType, const std::string &cameraName,
                                             const std::string &lines) {

    Json::Value lineJson;
    Json::CharReaderBuilder builder;
    std::istringstream iss(lines);
    std::string errs;
    if (!Json::parseFromStream(builder, iss, &lineJson, &errs)) {

        return;
    }

    std::vector<OverLineInfo> lineVec;
    for (auto line : lineJson) {
        OverLineInfo lineInfo;
        if (line.isMember("property")) {
            auto property = line["property"];
            if (property.isMember("description")) {
                lineInfo.description = property["description"].asString();
            }
            if (property.isMember("direction")) {
                lineInfo.direction = property["direction"].asInt();
            }
            if (property.isMember("name")) {
                lineInfo.line_name = property["name"].asString();
            }
        }
        if (line.isMember("start")) {
            auto start = line["start"];
            if (start.isMember("x")) {
                lineInfo.start_points_x = start["x"].asString();
            }
            if (start.isMember("y")) {
                lineInfo.start_points_y = start["y"].asString();
            }
        }

        if (line.isMember("end")) {
            auto end = line["end"];
            if (end.isMember("x")) {
                lineInfo.end_points_x = end["x"].asString();
            }
            if (end.isMember("y")) {
                lineInfo.end_points_y = end["y"].asString();
            }
        }

        lineInfo.camera_id = cameraId;
        lineInfo.camera_name = cameraName;
        
        lineVec.push_back(lineInfo);
    }

    if (lineVec.size() > 0) {
        DB::PreparedStatement *stmt = DB::LogonDatabasePool.GetPreparedStatement(AI_BOX_CAMERA_LINES_DELETE);
        stmt->SetInt32(0, cameraId);
        DB::LogonDatabasePool.Query(stmt);
        
        auto startTime = std::time(nullptr);
        for (const auto &item : lineVec) {
            
            DB::PreparedStatement *stmt2 = DB::LogonDatabasePool.GetPreparedStatement(AI_BOX_CAMERA_LINES_ADD);
            stmt2->SetInt32(0, item.camera_id);
            stmt2->SetString(1, item.camera_name);
            stmt2->SetString(2, item.line_name);
            stmt2->SetInt64(3, startTime);
            stmt2->SetInt32(4, 0);
            stmt2->SetInt32(5, aglType);
            stmt2->SetInt32(6, item.direction);
            stmt2->SetInt32(7, 0);
            stmt2->SetString(8, item.start_points_x);
            stmt2->SetString(9, item.start_points_y);
            stmt2->SetString(10, item.end_points_x);
            stmt2->SetString(11, item.end_points_y);
            stmt2->SetString(12, item.description);
            stmt2->SetInt32(13, item.enable);

            DB::PreparedQueryResult query2 = DB::LogonDatabasePool.Query(stmt2);
        }
    }
}
