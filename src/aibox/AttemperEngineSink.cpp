#include "AttemperEngineSink.h"
#include "AIBoxListManager.h"
#include "AiBoxDefine.h"
#include "CMD_AIBox.h"
#include "CMD_Correspond.h"
#include "CMD_LogonServer.h"
#include "Header.h"
#include "INIReader.h"
#include "Implementation/LogonDatabase.h"
#include "Log.h"
#include "Packet.h"
#include "ServiceUnits.h"
#include "StringFormat.h"
#include "StringUtility.h"
#include "Struct.h"
#include "algconfig.h"
#include "data/CDirManager.h"
#include "data/CGlobal.h"
#include "http/handler/CTools.h"

#include <chrono>
#include <iostream>
#include <string>

#define MAX_LINK_COUNT 512
#define OPEN_SWITCH 1
#define CLIENT_SWITCH 0

namespace AiBox
{
using namespace LogComm;
using namespace Comm;
using namespace Util;

#define IDI_LOAD_GAME_LIST 1        // �����б�
#define IDI_CONNECT_CORRESPOND 2    // ������ʶ
#define IDI_CONNECT_CAMERA 3        // ����������������ͷ��Ϣ
#define IDI_CLEAN_ALARM_LOG 4       // 清除报警日志
#define IDI_MAINBOARD_STA 5         // 定时查询主板状态
#define IDI_NOTICE_BOX_UPDATE_ALG 6 // 定时更新边缘计算盒算法

#define LOGON_FAILURE(linkid, errorcode)                                                                                                             \
    if (OnLogonFailure(linkid, errorcode))                                                                                                           \
    {                                                                                                                                                \
        return true;                                                                                                                                 \
    }

CAttemperEngineSink::CAttemperEngineSink() {}

CAttemperEngineSink::~CAttemperEngineSink() {}

void CAttemperEngineSink::Release() {}

void *CAttemperEngineSink::QueryInterface(GGUID uuid)
{
    QUERY_INTERFACE(IAttemperEngineSink, uuid);
    QUERY_INTERFACE_IUNKNOWNEX(IAttemperEngineSink, uuid);
    return nullptr;
}

bool CAttemperEngineSink::OnAttemperEngineStart(IUnknownEx *pIUnknownEx)
{
    m_pBindParameter = new tagBindParameter[MAX_LINK_COUNT];
    m_pITimerEngine->SetTimer(IDI_CLEAN_ALARM_LOG, 1000, 1);
    return true;
}

bool CAttemperEngineSink::OnAttemperEngineConclude(IUnknownEx *pIUnknownEx)
{
    m_pITCPSocketService = nullptr;
    return false;
}

bool CAttemperEngineSink::OnEventTCPSocketLink(uint16 wServiceID, int iErrorCode)
{
    // Э������
    if (wServiceID == NETWORK_CORRESPOND)
    {
        // �����ж�
        if (iErrorCode != 0)
        {
            int iConnectTime = sConfigMgr->GetInt32("LocalNet", "ConnectTime", 5);
            LOG_INFO("server.logon",
                     "Correspond server connection failed [ %d ], will "
                     "reconnect in %d seconds",
                     iErrorCode, iConnectTime);
            // m_pITimerEngine->SetTimer(IDI_CONNECT_CORRESPOND, iConnectTime *
            // 1000, 1);
            return false;
        }

        // ��ʾ��Ϣ
        LOG_INFO("server.logon", "Registering Loginserver...");

        // ��������
        CMD_CS_C_RegisterLogon RegisterLogon;
        memset(&RegisterLogon, 0, sizeof(RegisterLogon));

        // ���ñ���
#if LENDY_PLATFORM == LENDY_PLATFORM_WINDOWS
        sprintf_s(RegisterLogon.szServerName, "%s", sConfigMgr->Get("LocalNet", "Name", "").c_str());
        sprintf_s(RegisterLogon.szServerAddr, "%s", sConfigMgr->Get("LocalNet", "WinBindIP", "").c_str());
#else
        snprintf(RegisterLogon.szServerName, sizeof(RegisterLogon.szServerName), "%s", sConfigMgr->Get("LocalNet", "Name", "").c_str());
        snprintf(RegisterLogon.szServerAddr, sizeof(RegisterLogon.szServerAddr), "%s", sConfigMgr->Get("LocalNet", "LinuxBindIP", "").c_str());
#endif
        std::cout << "SUB_CS_C_REGISTER_LOGON1111" << std::endl;
        // ��������
        m_pITCPSocketService->SendData(MDM_CS_REGISTER, SUB_CS_C_REGISTER_LOGON, &RegisterLogon, sizeof(RegisterLogon));

        return true;
    }

    return true;
}

bool CAttemperEngineSink::OnEventTCPSocketShut(uint16 wServiceID, uint8 cbShutReason)
{
    // Э������
    if (wServiceID == NETWORK_CORRESPOND)
    {
        // �����ж�
        // if (m_bNeekCorrespond == true)
        {
            // ��ʾ��Ϣ
            int iConnectTime = sConfigMgr->GetInt32("LocalNet", "ConnectTime", 5);
            LOG_INFO("server.logon",
                     "The connection to the correspond server is closed, and "
                     "will reconnect in %d seconds",
                     iConnectTime);

            // ����ʱ��
            assert(m_pITimerEngine != nullptr);
            // m_pITimerEngine->SetTimer(IDI_CONNECT_CORRESPOND, iConnectTime *
            // 1000, 1);
            return true;
        }
    }
    return false;
}

bool CAttemperEngineSink::OnEventTCPSocketRead(uint16 wServiceID, TCP_Command Command, void *pData, uint16 wDataSize)
{
    // Э������
    if (wServiceID == NETWORK_CORRESPOND)
    {
        switch (Command.wMainCmdID)
        {
        case MDM_CS_REGISTER: // ע�����?
        {
            return OnTCPSocketMainRegister(Command.wSubCmdID, pData, wDataSize);
        }
        case MDM_CS_ROOM_INFO: // ������Ϣ
        {
            return OnTCPSocketMainServiceInfo(Command.wSubCmdID, pData, wDataSize);
        }
        }
    }
    return false;
}

bool CAttemperEngineSink::OnEventTCPNetworkBind(uint32 dwClientAddr, uint32 dwSocketID)
{
    // ��ȡ����
    assert(dwSocketID < MAX_LINK_COUNT);
    if (dwSocketID >= MAX_LINK_COUNT)
        return false;

    // ��������
    tagBindParameter *pBindParameter = (m_pBindParameter + dwSocketID);

    // ���ñ���
    pBindParameter->dwSocketID = dwSocketID;
    pBindParameter->dwClientAddr = dwClientAddr;
    std::cout << "Client connected: " << dwClientAddr << ", Socket ID: " << dwSocketID << std::endl;
    uint8 *pClientAddr = (uint8 *)&pBindParameter->dwClientAddr;
    std::string strClientIP = StringFormat("%d.%d.%d.%d", pClientAddr[3], pClientAddr[2], pClientAddr[1], pClientAddr[0]);
    LOG_INFO("server.aibox", "Client connected: %s, Socket ID: %d", strClientIP.c_str(), dwSocketID);

    return true;
}

bool CAttemperEngineSink::OnEventTCPNetworkShut(uint32 dwClientAddr, uint32 dwSocketID)
{
    memset((m_pBindParameter + dwSocketID), 0, sizeof(tagBindParameter));

    auto pBox = m_BoxListManager.GetAIBoxInfo(dwSocketID);
    if (pBox)
    {

        auto ptr = m_EnergyStorageCabinManager.GetEnergyStorageCabin(pBox->m_nDeviceLocationId);
        if (ptr && ptr->m_pMainBoardInfo)
        {
            ptr->m_pMainBoardInfo->m_bIsOnline = false;
        }
        m_BoxListManager.DelAIBoxInfo(dwSocketID);

        return true;
    }
    if (m_UserInfoMap.find(dwSocketID) != m_UserInfoMap.end())
    {
        m_UserInfoMap.erase(dwSocketID);
    }

    auto ptr = m_EnergyStorageCabinManager.GetEnergyStorageCabinBySocketId(dwSocketID);
    if (ptr && ptr->m_pMainBoardInfo)
    {
        ptr->m_pMainBoardInfo->m_bIsOnline = false;
    }

    //todo 通知前端掉线
    // m_EnergyStorageCabinManager.GetEnergyStorageCabinBySocketId(dwSocketID)->m_pMainBoardInfo->m_bIsOnline = false;

    return true;
}

bool CAttemperEngineSink::OnEventTCPNetworkRead(Net::TCP_Command Command, void *pData, uint16 wDataSize, uint32 dwSocketID)
{
    switch (Command.wMainCmdID)
    {
    case MDM_MB_LOGON: // ��¼����
    {
        return OnTCPNetworkMainMBLogon(Command.wSubCmdID, pData, wDataSize, dwSocketID);
    }
    case MDM_CS_SWARN:
    {

        return OnTCPNetworkMainCSImage(Command.wSubCmdID, pData, wDataSize, dwSocketID);
    }
    case MDM_CS_EDGEBOX:
    {
        return OnBoxSubEvent(Command.wSubCmdID, pData, wDataSize, dwSocketID);
    }
    case MDM_CS_TEMP:
    {
        return OnBoxTempEvent(Command.wSubCmdID, pData, wDataSize, dwSocketID);
    }
    case MDM_CS_CLIENT:
    {
        return OnClientMainEvent(Command.wSubCmdID, pData, wDataSize, dwSocketID);
    }
    case MDM_BS_MAINBOARD:
    {
        return OnMainBoarMainEvent(Command.wSubCmdID, pData, wDataSize, dwSocketID);
    }
    case MDM_CS_CAMERA:
    {
        std::cout << "------------MDM_CS_CAMERA------------" << std::endl;
        return true;
    }
    }
    return false;
}
bool CAttemperEngineSink::OnEventControl(uint16 wControlID, void *pData, uint16 wDataSize)
{
    switch (wControlID)
    {
    case SUC_LOAD_DB_GAME_LIST:
    {
        // ��ѯ����
        tagGameKind GameKind = {};
        GameKind.wKindID = 104;
        GameKind.wGameID = 104;

        GameKind.dwOnLineCount = 0;
        GameKind.dwAndroidCount = 0;
        GameKind.dwFullCount = 100;
        m_RoomListManager.InsertGameKind(&GameKind);

        // �¼�֪ͨ
        ControlResult ControlResult;
        ControlResult.cbSuccess = 1;
        SrvUnitsMgr->PostControlRequest(UDC_LOAD_DB_LIST_RESULT, &ControlResult, sizeof(ControlResult));
        return true;
    }
    case SUC_CONNECT_CORRESPOND:
    {
        // ��������
        // m_pITCPSocketService->Connect(sConfigMgr->Get("CorrespondNet", "BindIP", "127.0.0.1"), sConfigMgr->GetInt32("CorrespondNet", "Port",
        // 8600));
        return true;
    }
    case SUC_MDM_MB_CAMERA:
    {
        // onCameraStatusChange();
        return true;
    }
    case SUC_MDM_MB_SDEEPIMGLIB:
    {
        onUpdatesDeepimglib(pData, wDataSize);
        return true;
    }
    }
    return false;
}

void CAttemperEngineSink::onUpdatesDeepimglib(void *pData, uint16 wDataSize)
{
    TCP_block block;
    block.nblockcount = 1;
    block.nblockid = 1;
    block.nblockGUID = Util::generate_compressed_uuid();
    char buff[1024] = {0};
    memcpy(buff, &block, sizeof(block));
    memcpy(buff + sizeof(block), pData, wDataSize);
    std::string strData = std::string(buff, sizeof(block) + wDataSize);

    LOG_INFO("server.logon", "onUpdatesDeepimglib msgsize:%d oldDatasize[%d]", strData.size(), wDataSize);
    m_pITCPNetworkEngine->SendData(m_ClientInfo.dwSocketID, MDM_CS_ALG, SUB_CS_ALG_DLIB, (void *)strData.data(), strData.size());
}

void CAttemperEngineSink::onCameraStatusChange()
{

    std::cout << "---------------Camera control event received.-------------" << std::endl;
    DB::PreparedStatement *stmt = DB::LogonDatabasePool.GetPreparedStatement(AI_BOX_CAMERA_PUSH_STREAM);

    DB::PreparedQueryResult query = DB::LogonDatabasePool.Query(stmt);
    if (query)
    {
        // ��������
        std::vector<std::string> bindValues;
        std::string str = "";
        CameraInfolist cameraList;
        std::vector<int> cameraIdList;
        do
        {
            DB::Field *field = query->Fetch();

            CameraInfo cameraInfo;
            cameraInfo.ID = field[0].GetInt32();
            cameraInfo.IP = field[1].GetString();
            cameraInfo.RTSP = field[2].GetString();
            int start = field[3].GetInt32();
            if (start >= 1)
            {
                // todo �ж�start  1 �ŷ���
                cameraList.CameraInfoList.push_back(cameraInfo);

                cameraIdList.push_back(cameraInfo.ID);
            }

        } while (query->NextRow());

        std::string cameraconfig = xpack::json::encode(cameraList);
        TCP_block block;
        block.nblockcount = 1;
        block.nblockid = 1;
        block.nblockGUID = Util::generate_compressed_uuid();
        char buff[1024 * 10] = {0};
        memcpy(buff, &block, sizeof(block));
        memcpy(buff + sizeof(block), cameraconfig.data(), cameraconfig.size());
        std::string strData = std::string(buff, sizeof(block) + cameraconfig.size());

        std::cout << "block.nblockGUID:" << block.nblockGUID << "Camera configuration: " << cameraconfig << std::endl;
        m_pITCPNetworkEngine->SendData(m_ClientInfo.dwSocketID, MDM_CS_CAMERA, SUB_CS_CAMERA_INFO, (void *)strData.data(), strData.size());

        // ��ȡ����ͷ���ò���
        if (cameraIdList.size() > 0)
        {

            // DB::PreparedStatement *stmt1 =
            // DB::LogonDatabasePool.GetPreparedStatement(AI_BOX_CAMERA_ALG_CONF_LIST2);
            std::string strIdList = "";
            DB::vectorToString(cameraIdList, strIdList);
            // stmt1->SetString(0, strIdList);

            std::string lineSql = fmt::format("SELECT id, "
                                              "camera_id,type,direction,start_points_x,start_"
                                              "points_y,end_points_x,end_points_y "
                                              "FROM overline_log_table WHERE camera_id IN ( {} )",
                                              strIdList);
            auto lineQuery = DB::LogonDatabasePool.Query(lineSql.c_str());
            std::map<int, std::map<int, std::vector<scross_line>>> lineMap;
            if (lineQuery)
            {
                do
                {
                    scross_line line;
                    auto field = lineQuery->Fetch();
                    int index = 0;
                    auto id = field[index++].GetInt32();
                    auto cameraId = field[index++].GetInt32();
                    auto type = field[index++].GetInt32();
                    auto direction = field[index++].GetInt32();
                    auto start_points_x = field[index++].GetString();
                    auto start_points_y = field[index++].GetString();
                    auto end_points_x = field[index++].GetString();
                    auto end_points_y = field[index++].GetString();
                    line.dir = (orientation)direction;
                    line.line_id = id;

                    line.line_points1.x = std::stoi(start_points_x.c_str());
                    line.line_points1.y = std::stoi(start_points_y.c_str());

                    line.line_points2.x = std::stoi(end_points_x.c_str());
                    line.line_points2.y = std::stoi(end_points_y.c_str());
                    if (lineMap.find(cameraId) == lineMap.end())
                    {
                        std::vector<scross_line> v;
                        v.emplace_back(line);
                        std::map<int, std::vector<scross_line>> algMap;
                        algMap[type] = v;
                    }
                    else
                    {
                        auto algMap = lineMap.find(cameraId);
                        auto it = algMap->second.find(type);
                        if (it != algMap->second.end())
                        {
                            it->second.emplace_back(line);
                        }
                        else
                        {
                            std::vector<scross_line> v;
                            v.emplace_back(line);
                            algMap->second.insert(std::pair<int, std::vector<scross_line>>(type, v));
                        }
                    }

                } while (lineQuery->NextRow());
            }

            std::string cameraSql = fmt::format("SELECT `camera_id`,`alg_id`,`cycle`, "
                                                "`dp`,`dp_id`,`interval`,`hasTimes`,`level`, "
                                                "`people_count`,`thrs`,`vehicletype`,`vehs`,`lines`"
                                                ",`polygons`, `width`, `height`,`wb` FROM "
                                                "camera_alg_conf where `camera_id` in ( {} ) ;",
                                                strIdList);
            sParam_idlist cameraParamList;
            auto query1 = DB::LogonDatabasePool.Query(cameraSql.c_str());
            std::string jsonErr = "";
            if (query1)
            {
                do
                {
                    auto field = query1->Fetch();
                    // camera_id`,`alg_id`,`cycle`,
                    // `dp`,`dp_id`,`interval`,`hasTimes`,`level`, "
                    //"`people_count`,`thrs`,`vehicletype`,`vehs`,`lines`,`polygons`,
                    //`width`, `height`,`wb`
                    int index = 0;
                    int camera_id = field[index++].GetInt32();
                    int alg_id = field[index++].GetInt32();
                    int cycle = field[index++].GetInt32();
                    int dp = field[index++].GetInt32();
                    int dp_id = field[index++].GetInt32();
                    int interval = field[index++].GetInt32();
                    int hasTimes = field[index++].GetInt32();
                    int level = field[index++].GetInt32();
                    int people_count = field[index++].GetInt32();
                    std::string thrs = field[index++].GetString();
                    std::string vehicletype = field[index++].GetString();
                    std::string vehs = field[index++].GetString();
                    std::string lines = field[index++].GetString();
                    std::string polygons = field[index++].GetString();
                    int width = field[index++].GetInt32();
                    int height = field[index++].GetInt32();
                    int wb = field[index++].GetInt32();
                    sparam_id paramId;
                    paramId.cameraid = camera_id;

                    sParam param;
                    try
                    {

                        jsonErr.clear();
                        // thrs
                        Json::Value jVal = Util::stringToJson(thrs, jsonErr);
                        if (jVal.isArray())
                        {
                            for (auto item : jVal)
                            {
                                param.thrs.push_back(item.asFloat());
                            }
                        }

                        // lines
                        auto algMap = lineMap.find(camera_id);
                        if (algMap != lineMap.end())
                        {
                            auto lineVec = algMap->second.find(alg_id);
                            if (lineVec != algMap->second.end())
                            {
                                param.lines.insert(param.lines.end(), lineVec->second.begin(), lineVec->second.end());
                            }
                        }

                        // polygons
                        jsonErr.clear();
                        Json::Value polygonsVal = Util::stringToJson(polygons, jsonErr);
                        if (jsonErr.empty())
                        {
                            for (auto jv : polygonsVal)
                            {
                                auto polygonsValList = jv["points"];
                                std::vector<spoint> vec;
                                if (polygonsValList.isArray())
                                {
                                    for (auto v : polygonsVal)
                                    {

                                        spoint p;
                                        p.x = v["x"].asFloat();
                                        p.y = v["y"].asFloat();

                                        vec.emplace_back(p);
                                    }
                                }
                                if (vec.size() > 0)
                                {
                                    param.mask_points.emplace_back(vec);
                                }
                            }
                        }

                        // param.mask_points
                        param.Post_person = people_count;

                        param.warn_interval = interval;
                        param.warn_thr = hasTimes;
                        param.warn_window = cycle;
                        param.warn_level = level;

                        param.algid = (algtype)alg_id;
                    }
                    catch (const std::exception &e)
                    {
                        std::cerr << e.what() << '\n';
                    }

                    bool find = false;
                    for (auto &paramList : cameraParamList.algparamidList)
                    {
                        if (paramList.cameraid == camera_id)
                        {
                            paramList.param.emplace_back(param);
                            find = true;
                        }
                    }
                    if (!find)
                    {
                        paramId.param.emplace_back(param);
                        cameraParamList.algparamidList.emplace_back(paramId);
                    }

                } while (query1->NextRow());
            }

            std::string cameraconfigParam = xpack::json::encode(cameraParamList);
            int sendmax = 1024 * 10 - sizeof(TCP_block);
            int eleSize = cameraconfigParam.size();
            TCP_block block;
            block.nblockGUID = 999; // Util::generate_compressed_uuid();
            if (cameraconfigParam.size() > sendmax)
            {
                block.nblockcount = 1;
                if (cameraconfigParam.size() % sendmax == 0)
                {
                    block.nblockcount = cameraconfigParam.size() / sendmax;
                    eleSize = sendmax;
                }
                else
                {
                    block.nblockcount = cameraconfigParam.size() / sendmax + 1;
                    eleSize = sendmax;
                }
            }
            for (int i = 0; i < block.nblockcount; ++i)
            {
                // block.nblockcount = 1;
                block.nblockid = i + 1;
                char buff[1024 * 10] = {0};
                int temp = sendmax;
                if (cameraconfigParam.size() - (i * sendmax) < sendmax)
                {
                    temp = cameraconfigParam.size() - (i * sendmax);
                }
                memcpy(buff, &block, sizeof(block));
                memcpy(buff + sizeof(block), cameraconfigParam.data() + i * sendmax, temp);
                std::string strData = std::string(buff, sizeof(block) + temp);

                std::cout << "block.nblockid:" << block.nblockid << " temp:" << temp << std::endl;
                m_pITCPNetworkEngine->SendData(m_ClientInfo.dwSocketID, MDM_CS_ALG, SUB_CS_ALG_INFO, (void *)strData.data(), strData.size());
            }

            std::cout << " size: " << cameraconfigParam.size() << "-------------cameraconfigParam:" << cameraconfigParam << std::endl;
        }
    }
}

// ʱ���¼�
bool CAttemperEngineSink::OnEventTimer(uint32 dwTimerID)
{
    switch (dwTimerID)
    {
    case IDI_CONNECT_CORRESPOND:
    {
        // std::string strCorrespondAddress = sConfigMgr->Get("CorrespondNet", "BindIP", "127.0.0.1");
        // uint16 wCorrespondPort = sConfigMgr->GetInt32("CorrespondNet", "Port", 8600);
        // m_pITCPSocketService->Connect(strCorrespondAddress, wCorrespondPort);
        // LOG_INFO("server.logon", "Connecting to the correspond server [ %s:%d ]", strCorrespondAddress, wCorrespondPort);
        return true;
    }
    case IDI_CONNECT_CAMERA:
    {
        // onCameraStatusChange();
        return true;
    }
    case IDI_CLEAN_ALARM_LOG:
    {
        auto nTime = time(nullptr);
        int nDay = sConfigMgr->GetInt32("DEFINE", "AlarmLogDate", 86400);
        std::string alarmSql = fmt::format("delete from alarm_log_table where `date` < {} ;", (nTime - nDay));
        DB::LogonDatabasePool.Query(alarmSql.c_str());
        return true;
    }
    case IDI_MAINBOARD_STA:
    {

        return true;
    }
    case IDI_NOTICE_BOX_UPDATE_ALG:
    {

        return true;
    }
        //
    }
    return false;
}

bool CAttemperEngineSink::OnTCPSocketMainRegister(uint16 wSubCmdID, void *pData, uint16 wDataSize)
{
    switch (wSubCmdID)
    {
    case SUB_CS_S_REGISTER_FAILURE: // ע��ʧ��
    {
        // ��������
        CMD_CS_S_RegisterFailure *pRegisterFailure = (CMD_CS_S_RegisterFailure *)pData;

        // Ч�����?
        assert(wDataSize >= (sizeof(CMD_CS_S_RegisterFailure) - sizeof(pRegisterFailure->szDescribeString)));
        if (wDataSize < (sizeof(CMD_CS_S_RegisterFailure) - sizeof(pRegisterFailure->szDescribeString)))
            return false;

        // �رմ���
        // m_bNeekCorrespond = false;
        m_pITCPSocketService->CloseSocket();

        // ��ʾ��Ϣ
        LOG_INFO("server.logon", "%s", pRegisterFailure->szDescribeString);

        // �¼�֪ͨ
        ControlResult ControlResult;
        ControlResult.cbSuccess = 0;
        SrvUnitsMgr->PostControlRequest(UDC_CORRESPOND_RESULT, &ControlResult, sizeof(ControlResult));
        return true;
    }
    }

    return true;
}

bool CAttemperEngineSink::OnTCPSocketMainServiceInfo(uint16 wSubCmdID, void *pData, uint16 wDataSize)
{
    switch (wSubCmdID)
    {
    case SUB_CS_S_ROOM_INFO: // ������Ϣ
    {
        m_RoomListManager.DisuseRoomItem();
        return true;
    }
    case SUB_CS_S_ROOM_ONLINE: // ��������
    {
        // Ч�����?
        assert(wDataSize == sizeof(CMD_CS_S_RoomOnLine));
        if (wDataSize != sizeof(CMD_CS_S_RoomOnLine))
            return false;

        // ��������
        CMD_CS_S_RoomOnLine *pServerOnLine = (CMD_CS_S_RoomOnLine *)pData;

        tagGameRoom *pRoomItem = m_RoomListManager.SearchGameRoom(pServerOnLine->wServerID);
        if (pRoomItem == nullptr)
            return true;

        uint32 dwOldOnlineCount = 0, dwOldAndroidCount = 0;
        dwOldOnlineCount = pRoomItem->dwOnLineCount;
        dwOldAndroidCount = pRoomItem->dwAndroidCount;

        // Ŀ¼����
        tagGameKind *pGameKindItem = m_RoomListManager.SearchGameKind(pRoomItem->wKindID);
        if (pGameKindItem != NULL)
        {
            // ��������
            pGameKindItem->dwOnLineCount -= dwOldOnlineCount;
            pGameKindItem->dwOnLineCount += pRoomItem->dwOnLineCount;

            // ��������
            pGameKindItem->dwAndroidCount -= dwOldAndroidCount;
            pGameKindItem->dwAndroidCount += pRoomItem->dwAndroidCount;
        }
        return true;
    }
    case SUB_CS_S_ROOM_REMOVE: // ����ɾ��
    {
        // Ч�����?
        assert(wDataSize == sizeof(CMD_CS_S_RoomRemove));
        if (wDataSize != sizeof(CMD_CS_S_RoomRemove))
            return false;

        // ��������
        CMD_CS_S_RoomRemove *pRoomRemove = (CMD_CS_S_RoomRemove *)pData;

        m_RoomListManager.DeleteGameRoom(pRoomRemove->wServerID);
        return true;
    }
    case SUB_CS_S_ROOM_INSERT: // �������?
    {
        // Ч�����?
        assert(wDataSize % sizeof(tagGameRoom) == 0);
        if (wDataSize % sizeof(tagGameRoom) != 0)
            return false;

        // ��������
        uint16 wItemCount = wDataSize / sizeof(tagGameRoom);
        tagGameRoom *pGameRoom = (tagGameRoom *)pData;

        // ��������
        for (uint16 i = 0; i < wItemCount; ++i)
        {
            m_RoomListManager.InsertGameRoom(pGameRoom++);
        }
        return true;
    }
    case SUB_CS_S_ROOM_FINISH: // �������?
    {
        // �¼�����
        ControlResult ControlResult;
        ControlResult.cbSuccess = 1;
        SrvUnitsMgr->PostControlRequest(UDC_CORRESPOND_RESULT, &ControlResult, sizeof(ControlResult));
        std::cout << "SUB_CS_S_ROOM_FINISH----------------------------" << std::endl;
        return true;
    }
    }
    return false;
}

bool CAttemperEngineSink::OnTCPNetworkMainMBLogon(uint16 wSubCmdID, void *pData, uint16 wDataSize, uint32 dwSocketID)
{
    switch (wSubCmdID)
    {
    case SUB_MB_LOGON_VISITOR: // �ο͵�¼
    {
        return OnTCPNetworkSubMBLogonVisitor(pData, wDataSize, dwSocketID);
    }
    }
    return false;
}

bool CAttemperEngineSink::OnTCPNetworkMainCSImage(uint16 wSubCmdID, void *pData, uint16 wDataSize, uint32 dwSocketID)
{
    switch (wSubCmdID)
    {

    case SUB_CS_SWARN_INFO: // ������Ϣ
    {
        return OnTCPNetworkSubCSImage(pData, wDataSize, dwSocketID);
    }
    }
    return false;
}

bool CAttemperEngineSink::OnTCPNetworkSubTempInfo(void *pData, uint16 wDataSize, uint32 dwSocketID)
{
    if (wDataSize < sizeof(TCP_block))
    {
        return false;
    }

    std::string jdata((char *)(pData + sizeof(TCP_block)), wDataSize - sizeof(TCP_block));
    sTemp tempinfo;
    try
    {
        xpack::json::decode(jdata, tempinfo);
        LOG_INFO("server.aibox", "OnTCPNetworkSubTempInfo cameraid:[%d] temp:[%f]", tempinfo.cameraid, tempinfo.temp);
        UpdateEnergyStorageCabinTemp(tempinfo.cameraid, tempinfo.temp);

        NoticeClientTemp(tempinfo.cameraid, tempinfo.temp);
        return true;
    }
    catch (const std::exception &e)
    {
        LOG_INFO("server.aibox", "OnTCPNetworkSubTempInfo err:[%s]", e.what());
    }
    return false;
}

void CAttemperEngineSink::NoticeClientTemp(uint32 nCameraid, float fTemp)
{
    CameraDevInfoPtr pCameraInfo;
    m_BoxListManager.FindCameraByCameraId(nCameraid, pCameraInfo);
    if (pCameraInfo != NULL && pCameraInfo->m_nLocationType == EnergyStorageCabin)
    {

        CMD_SC_Client_Temp clientTemp;
        auto energyCabinPtr = m_EnergyStorageCabinManager.GetEnergyStorageCabin(pCameraInfo->m_nLocationId);
        if (energyCabinPtr)
        {
            clientTemp.m_nCabinNum = energyCabinPtr->m_sCode;
        }
        clientTemp.nCabinId = pCameraInfo->m_nLocationId;
        clientTemp.nCameraid = nCameraid;
        clientTemp.fTemp = fTemp;
        for (auto &client : m_UserInfoMap)
        {
            m_pITCPNetworkEngine->SendData(client.second->dwSocketID, MDM_CS_CLIENT, SUB_SC_CLIENT_TEMP, &clientTemp, sizeof(clientTemp));
        }
        LOG_INFO("server.aibox", "send client temp nCabinId:[%d],nCameraid:[%d],fTemp:[%f]", clientTemp.nCabinId, clientTemp.nCameraid,
                 clientTemp.fTemp);
    }
}

void CAttemperEngineSink::UpdateEnergyStorageCabinTemp(uint32 uCameraID, float fTemp)
{
    CameraDevInfoPtr pCameraInfo;
    m_BoxListManager.FindCameraByCameraId(uCameraID, pCameraInfo);
    if (pCameraInfo == nullptr)
    {
        LOG_INFO("server.aibox", "OnTCPNetworkSubTempInfo cameraid:[%d] not found", uCameraID);
        return;
    }
    uint32 temp = (uint32)(fTemp * 100);

    if (pCameraInfo != NULL && pCameraInfo->m_nLocationType == EnergyStorageCabin)
    {
        std::string sql = fmt::format("update energy_storage_cabin_table set `temp` = {} where `id` = {}", temp, pCameraInfo->m_nLocationId);
        DB::LogonDatabasePool.Query(sql.c_str());

        auto pEnergyStorageCabin = m_EnergyStorageCabinManager.GetEnergyStorageCabin(pCameraInfo->m_nLocationId);
        if (pEnergyStorageCabin)
        {
            pEnergyStorageCabin->m_uTemp = temp;
        }
    }
}

bool CAttemperEngineSink::OnTCPNetworkSubTempWarn(void *pData, uint16 wDataSize, uint32 dwSocketID)
{
    if (wDataSize < sizeof(TCP_block))
    {
        return false;
    }

    std::string jdata((char *)(pData + sizeof(TCP_block)), wDataSize - sizeof(TCP_block));
    sTempwarn tempwarn;
    try
    {
        xpack::json::decode(jdata, tempwarn);

        LOG_INFO("server.aibox",
                 "OnTCPNetworkSubTempWarn cameraid:[%d] hightemp:[%f] "
                 "imgpath:[%s] m_nTime:[%lld]",
                 tempwarn.cameraid, tempwarn.hightemp, tempwarn.imgpath, tempwarn.m_nTime);
        // ���?
        auto stmt = DB::LogonDatabasePool.GetPreparedStatement(AI_BOX_CAMERA_ALARM_LOG_INSERT);
        CameraDevInfoPtr pCameraInfo;
        m_BoxListManager.FindCameraByCameraId(tempwarn.cameraid, pCameraInfo);

        BoxPtr pAIBoxInfo = m_BoxListManager.GetAIBoxInfo(dwSocketID);
        auto ses = secondsSinceMidnight();
        stmt->SetInt32(0, tempwarn.cameraid);
        if (pCameraInfo)
        {
            stmt->SetString(1, pCameraInfo->m_sName);
            stmt->SetString(2, std::to_string(pCameraInfo->m_nDevice_id));
            stmt->SetInt32(3, pCameraInfo->m_nLocationId);
            stmt->SetInt32(4, (int)EnergyStorageCabin);
        }

        stmt->SetInt64(5, tempwarn.m_nTime);

        if (pAIBoxInfo)
        {
            tempwarn.imgpath = "\\\\" + pAIBoxInfo->m_sIp + tempwarn.imgpath;
        }
        stmt->SetInt64(6, ALARM_TYPE_TEMP);
        stmt->SetInt64(7, ALARM_TYPE_TEMP); //
        stmt->SetString(8, tempwarn.imgpath);
        stmt->SetInt64(9, int(tempwarn.hightemp * 100));

        DB::LogonDatabasePool.Query(stmt);

        // 更新数据库状态
        std::string sql = fmt::format("update camera_info_table set `status` = {} where `id` = {}", CAMERA_STATUS_ALARM, tempwarn.cameraid);
        DB::LogonDatabasePool.Query(sql.c_str());

        UpdateEnergyStorageCabinTemp(tempwarn.cameraid, tempwarn.hightemp);

        NoticeClientWarning(tempwarn.cameraid, tempwarn.hightemp, tempwarn.m_nTime, ALARM_TYPE_TEMP, tempwarn.imgpath);

        return true;
    }
    catch (const std::exception &e)
    {
        LOG_WARN("server.aibox", "OnTCPNetworkSubTempWarn err:[%s]", e.what());
    }

    return false;
}

void CAttemperEngineSink::NoticeClientWarning(uint32 nCameraid, float fHightemp, long long llTime, uint8_t uWarnType, const std::string &sImgpath)
{
    CMD_SC_Client_Warn tempwarnResq;
    CameraDevInfoPtr pCameraInfo;
    m_BoxListManager.FindCameraByCameraId(nCameraid, pCameraInfo);
    if (pCameraInfo != nullptr)
    {
        auto pEnergyStorageCabin = m_EnergyStorageCabinManager.GetEnergyStorageCabin(pCameraInfo->m_nLocationId);
        if (pEnergyStorageCabin)
        {
            tempwarnResq.m_nCabinNum = pEnergyStorageCabin->m_sCode;
        }
    }

    tempwarnResq.nCameraid = nCameraid;
    tempwarnResq.fHightemp = fHightemp;
    tempwarnResq.llTime = llTime;
    tempwarnResq.uWarnType = uWarnType;
    memcpy(tempwarnResq.sImgpath, sImgpath.c_str(), sImgpath.length());
    for (auto &client : m_UserInfoMap)
    {
        m_pITCPNetworkEngine->SendData(client.second->dwSocketID, MDM_CS_CLIENT, SUB_SC_CLIENT_WARN, &tempwarnResq, sizeof(tempwarnResq));
    }
}

bool CAttemperEngineSink::OnBoxSubEvent(uint16 wSubCmdID, void *pData, uint16 wDataSize, uint32 dwSocketID)
{
    switch (wSubCmdID)
    {
    case SUB_CS_EDGEBOX_INFO: //
    {
        return OnBoxRegister(pData, wDataSize, dwSocketID);
    }
    }
    return false;
}

bool CAttemperEngineSink::OnClientMainEvent(uint16 wSubCmdID, void *pData, uint16 wDataSize, uint32 dwSocketID)
{
    switch (wSubCmdID)
    {
    case SUB_CS_CLIENT_REGISTER: // 客户端注册
    {
        return OnClientRegister(pData, wDataSize, dwSocketID);
    }
    case SUB_CS_CLIENT_QUERY_MSG: // 客户端查询
    {
        return OnClientQueryMsg(pData, wDataSize, dwSocketID);
    }
    case SUB_CS_CLIENT_DEV_OPEN: // 客户端操作
    {
        return OnClientDevOpen(pData, wDataSize, dwSocketID);
    }
    case SUB_SC_CLIENT_CABIN_STA: // 客户端查询状态
    {
        return OnClientCabinSta(pData, wDataSize, dwSocketID);
    }
    case SUB_CS_CLIENT_DRAW_AREA: // 花区域
    {
        return OnClientDrawArea(pData, wDataSize, dwSocketID);
    }
    case SUB_CS_CLIENT_DISPOSE_ALAEM: // 客户端处理报警
    {
        return OnClientDisposeAlarm(pData, wDataSize, dwSocketID);
    }
    }
    return false;
}

bool CAttemperEngineSink::OnClientDisposeAlarm(void *pData, uint16 wDataSize, uint32 dwSocketID)
{
    if (wDataSize < sizeof(CMD_CS_Dispose_Alarm))
    {
        LOG_INFO("server.aibox", "OnClientDisposeAlarm wDataSize:[%d] < sizeof(CMD_CS_AreaPos)", wDataSize);
        return false;
    }
    CMD_CS_Dispose_Alarm *pDisposeAlarm = (CMD_CS_Dispose_Alarm *)pData;

    std::string sql = fmt::format("update alarm_log_table set is_do = {} where id = {}", pDisposeAlarm->m_nDispose, pDisposeAlarm->m_nId);
    LogonDatabasePool.Query(sql.c_str());

    m_pITCPNetworkEngine->SendData(dwSocketID, MDM_CS_CLIENT, SUB_SC_CLIENT_DISPOSE_ALAEM_RESQ, NULL, 0);

    return true;
}

void CAttemperEngineSink::UpdateCameraStatus(int nCameraId, int status, uint32 dwSocketID)
{
    // 更新摄像机状态
    std::string updateCameraSta = fmt::format("update camera_info_table set camera_cfg_sta = {} where `id` = {}", status, nCameraId);
    DB::LogonDatabasePool.Query(updateCameraSta.c_str());
    st_SC_SaveAreaPosRESQ resq;
    resq.m_nCamearID = nCameraId;
    m_pITCPNetworkEngine->SendData(dwSocketID, MDM_CS_CLIENT, SUB_SC_CLIENT_DRAW_AREA_RESQ, &resq, sizeof(resq));

    // 通知边缘计算盒更新算法
    auto pBox = m_BoxListManager.GetAIBoxInfoByCameraId(nCameraId);
    if (pBox)
    {

        std::string sCameraList = "";
        for (auto &v : pBox->m_vCameraList)
        {
            sCameraList += std::to_string(v->m_nId) + ",";
        }
        sCameraList.pop_back();
        LOG_INFO("server.aibox", "SAglCendWaiWeiCameraonfig:m_nSocketId:[%d] sCameraList:[%s]", pBox->m_nSocketId, sCameraList);
        SendWaiWeiCameraAglConfig(pBox->m_nSocketId, pBox->m_nBoxId, sCameraList);
    }
}


bool CAttemperEngineSink::OnClientDrawArea(void *pData, uint16 wDataSize, uint32 dwSocketID)
{

    if (wDataSize < sizeof(CMD_CS_AreaPos))
    {
        LOG_INFO("server.aibox", "OnClientDrawArea wDataSize:[%d] < sizeof(CMD_CS_AreaPos)", wDataSize);
        return false;
    }

    int alg_id = 1006;
    CMD_CS_AreaPos *pAreaPos = (CMD_CS_AreaPos *)pData;

    int cameraId = pAreaPos->m_pos.m_nCameraId;
     LOG_INFO("server.aibox", "OnClientDrawArea cameraId:[%d] m_AreaCnt :[%d]", cameraId,pAreaPos->m_pos.m_AreaCnt);
    if (pAreaPos->m_pos.m_AreaCnt == 0)
    {
        // 删除绘制区域
        std::string delSql = fmt::format("delete from camera_alg_conf where `camera_id` = {} and `alg_id` = 1006", cameraId);
        DB::LogonDatabasePool.Query(delSql.c_str());

        UpdateCameraStatus(cameraId, 1, dwSocketID);
        return true;
    }

    Json::Value areaList;
    for (int i = 0; i < pAreaPos->m_pos.m_AreaCnt && i < DRAW_AREA_MAX_AREA; i++)
    {
        Json::Value area;
        Json::Value points;
        for (int j = 0; j < DRAW_AREA_MAX_POINT; j++)
        {
            if (pAreaPos->m_pos.m_szPos[i][j].m_posX == 0 && pAreaPos->m_pos.m_szPos[i][j].m_posY == 0)
            {
                continue;
            }

            Json::Value p;
            p["x"] = (float)pAreaPos->m_pos.m_szPos[i][j].m_posX / 100.0;
            p["y"] = (float)pAreaPos->m_pos.m_szPos[i][j].m_posY / 100.0;

            points.append(p);
        }
        area["points"] = points;
        areaList.append(area);
    }

    int cycle = 5;
    int dp = 1;
    int dp_id = 1;
    int interval = 5;
    int hasTimes = 4;
    int level = 1;
    int people_count = 5;
    std::string thrs = "[0.75]";
    std::string vehicletype = "[]";
    std::string vehs = "[]";
    std::string polygons = areaList.toStyledString();
    int width = 1920;
    int height = 1080;
    int wb = 1;
    std::string lines = "[]";

    // 先判断有没有这个算法，有就更新
    std::string sql = fmt::format("select count(*) from camera_alg_conf where `camera_id` = {} and `alg_id` = 1006", cameraId);
    auto result = DB::LogonDatabasePool.Query(sql.c_str());
    if (result)
    {
        DB::Field *field = result->Fetch();
        int i = 0;
        int count = field[i++].GetInt32();
        if (count > 0)
        {
            DB::PreparedStatement *stmtAlg1 = DB::LogonDatabasePool.GetPreparedStatement(AI_BOX_CAMERA_ALG_CONF_UPDATE);

            stmtAlg1->SetInt32(0, cycle);
            stmtAlg1->SetInt32(1, dp);
            stmtAlg1->SetInt32(2, dp_id);
            stmtAlg1->SetInt32(3, interval);
            stmtAlg1->SetInt32(4, hasTimes);
            stmtAlg1->SetInt32(5, level);
            stmtAlg1->SetInt32(6, people_count);
            stmtAlg1->SetString(7, thrs);
            stmtAlg1->SetString(8, vehicletype);
            stmtAlg1->SetString(9, vehs);
            stmtAlg1->SetString(10, lines);
            stmtAlg1->SetString(11, polygons);
            stmtAlg1->SetInt32(12, width);
            stmtAlg1->SetInt32(13, height);
            stmtAlg1->SetInt32(14, wb);
            stmtAlg1->SetInt32(15, cameraId);
            stmtAlg1->SetInt32(16, alg_id);
            DB::LogonDatabasePool.Query(stmtAlg1);
        }
        else
        {
            DB::PreparedStatement *stmtAlg = DB::LogonDatabasePool.GetPreparedStatement(AI_BOX_CAMERA_ALG_CONF_ADD);

            stmtAlg->SetInt32(0, cameraId);
            stmtAlg->SetInt32(1, alg_id);
            stmtAlg->SetInt32(2, cycle);
            stmtAlg->SetInt32(3, dp);
            stmtAlg->SetInt32(4, dp_id);
            stmtAlg->SetInt32(5, interval);
            stmtAlg->SetInt32(6, hasTimes);
            stmtAlg->SetInt32(7, level);
            stmtAlg->SetInt32(8, people_count);
            stmtAlg->SetString(9, thrs);
            stmtAlg->SetString(10, vehicletype);
            stmtAlg->SetString(11, vehs);
            stmtAlg->SetString(12, lines);
            stmtAlg->SetString(13, polygons);
            stmtAlg->SetInt32(14, width);
            stmtAlg->SetInt32(15, height);
            stmtAlg->SetInt32(16, wb);
            DB::LogonDatabasePool.Query(stmtAlg);
        }
    }

    UpdateCameraStatus(cameraId, 0, dwSocketID);

    

    
    // m_pITimerEngine->SetTimer(IDI_NOTICE_BOX_UPDATE_ALG, 3 * 1000, 1);

    return true;
}

bool CAttemperEngineSink::OnClientCabinSta(void *pData, uint16 wDataSize, uint32 dwSocketID)
{
    std::string sql = "select id,code,name from energy_storage_cabin_table ";
    DB::QueryResult result = DB::LogonDatabasePool.Query(sql.c_str());
    if (result)
    {
        CMD_SC_Energ_Cabin_Sta cabin;
        int m = 0;
        do
        {

            DB::Field *field = result->Fetch();
            int i = 0;
            int id = field[i++].GetInt32();
            int code = field[i++].GetInt32();
            std::string name = field[i++].GetString();
            st_Cabin_Sta cabinSta;

            // cabinSta.nCabinId = id;
            auto pEnergyStorageCabin = m_EnergyStorageCabinManager.GetEnergyStorageCabin(id);
            memcpy(cabinSta.m_8CabinName, name.c_str(), name.length());
            cabinSta.m_nCabinNum = code;
            cabinSta.m_nCabinID = id;
            cabinSta.m_u8CabinSta = 1;
            if (pEnergyStorageCabin && pEnergyStorageCabin->m_pMainBoardInfo)
            {
                cabinSta.m_u8CabinSta = pEnergyStorageCabin->m_pMainBoardInfo->m_bIsOnline ? 0 : 1;
                cabinSta.m_u8AlarmLevel = 0;
                cabinSta.m_u16MaxTemper = pEnergyStorageCabin->m_uTemp;

                for (auto cabinDev : pEnergyStorageCabin->m_pMainBoardInfo->m_vDevStatus){
                    if (cabinDev.type == YI_TI_JI){
                        if (cabinDev.id > YITIJI_CNT_IN_CABIN){
                            break;
                        }
                        cabinSta.m_u8YiTiJiSta[cabinDev.id-1] = cabinDev.sta;
                        LOG_INFO("server.aibox", "SendWaiWeiCameraAglConfig:Cabinid:[%d] j:[%d]  m_u8YiTiJiSta:[%d]", id, cabinDev.id - 1,
                                 cabinSta.m_u8YiTiJiSta[cabinDev.id - 1]);
                    }
                }
                    // for (int j = 0; j < YITIJI_CNT_IN_CABIN && j < pEnergyStorageCabin->m_pMainBoardInfo->m_vDevStatus.size(); j++)
                    // {
                    //     cabinSta.m_u8YiTiJiSta[j] = pEnergyStorageCabin->m_pMainBoardInfo->m_vDevStatus[j].sta;
                    //     LOG_INFO("server.aibox", "SendWaiWeiCameraAglConfig:Cabinid:[%d] j:[%d]  m_u8YiTiJiSta:[%d]", id, j,
                    //              cabinSta.m_u8YiTiJiSta[j]);
                    // }
                for (auto cabinDev : pEnergyStorageCabin->m_pMainBoardInfo->m_vDevStatus)
                {
                    if (cabinDev.type == MEI_HUO_DAN)
                    {
                        cabinSta.m_u8OutFireBomb = cabinDev.sta;
                        LOG_INFO("server.aibox", "SendWaiWeiCameraAglConfig:Cabinid:[%d] m_nCabinNum:[%d] m_u8OutFireBomb:[%d]", id,
                                 cabinSta.m_nCabinNum, cabinSta.m_u8OutFireBomb);
                        break;
                    }
                }
            }
            cabin.m_data[m++] = cabinSta;

        } while (result->NextRow());
        m_pITCPNetworkEngine->SendData(dwSocketID, MDM_CS_CLIENT, SUB_SC_CLIENT_CABIN_STA_RESQ, &cabin, sizeof(CMD_SC_Energ_Cabin_Sta));
    }
    return true;
}

bool CAttemperEngineSink::OnMainBoarMainEvent(uint16 wSubCmdID, void *pData, uint16 wDataSize, uint32 dwSocketID)
{
    switch (wSubCmdID)
    {
    case 0: // 处理子命令为0的情况
    {
        LOG_INFO("server.aibox", "OnMainBoarMainEvent received subCmd=0, dataSize=%d, socketID=%d", wDataSize, dwSocketID);
        // 这可能是主板发送的某种确认或状态消息
        // 暂时只记录日志，不做特殊处理
        return true;
    }
    case SUB_BS_MAINBOARD_REGISTER: // 主板注册
    {
        return OnMainBoardRegister(pData, wDataSize, dwSocketID);
    }
    case SUB_SB_MAINBOARD_DEV_STATUS: // 主板状态
    {
        return OnMainBoardStatus(pData, wDataSize, dwSocketID);
    }
    case SUB_SB_MAINBOARD_DEV_OPT_RESQ: // 主板操作返回
    {
        return OnMainBoardDevOptResq(pData, wDataSize, dwSocketID);
    }
    case SUB_SB_MAINBOARD_DEV_ONLINE_RESQ: // 灭火弹上线
    {
        return OnMainBoardDevOnlineResq(pData, wDataSize, dwSocketID);
    }
    default:
    {
        LOG_INFO("server.aibox", "OnMainBoarMainEvent unknown subCmd=%d, dataSize=%d, socketID=%d", wSubCmdID, wDataSize, dwSocketID);
        return false;
    }
    }
    return false;
}

bool CAttemperEngineSink::OnMainBoardDevOnlineResq(void *pData, uint16 wDataSize, uint32 dwSocketID)
{
    if (wDataSize < sizeof(CMD_BS_Mainboard_Dev_Online_Resq))
    {
        LOG_INFO("server.aibox", "OnMainBoardDevOnlineResq wDataSize:[%d]", wDataSize);
        return false;
    }

    CMD_BS_Mainboard_Dev_Online_Resq *pResq = (CMD_BS_Mainboard_Dev_Online_Resq *)pData;
    LOG_INFO("server.aibox", " OnMainBoardDevOnlineResq id:[%d] type:[%d] sta:[%d]", pResq->id, pResq->type, pResq->sta);
    // TODO 通知前端
    auto pEnergyStorageCabin = m_EnergyStorageCabinManager.GetEnergyStorageCabinBySocketId(dwSocketID);
    if (pEnergyStorageCabin)
    {
        for (auto &it : pEnergyStorageCabin->m_pMainBoardInfo->m_vDevStatus)
        {
            if (it.id == pResq->id && it.type == pResq->type)
            {
                it.sta = pResq->sta;
                break;
            }
        }
    }
    for (auto &client : m_UserInfoMap)
    {

        CMD_CS_Dev_Open_Resq clientDevOptResq;
        clientDevOptResq.uCabinId = pEnergyStorageCabin->m_sCode;
        clientDevOptResq.uTpye = pResq->type;
        clientDevOptResq.uOptType = 2;
        clientDevOptResq.uDevId = pResq->id;
        m_pITCPNetworkEngine->SendData(client.second->dwSocketID, MDM_CS_CLIENT, SUB_CS_CLIENT_DEV_OPEN_RESQ, &clientDevOptResq,
                                       sizeof(clientDevOptResq));
    }
    return true;
}

bool CAttemperEngineSink::OnMainBoardDevOptResq(void *pData, uint16 wDataSize, uint32 dwSocketID)
{
    if (wDataSize < sizeof(CMD_BS_Mainboard_Dev_Opt_Resq))
    {
        LOG_INFO("server.aibox", "OnMainBoardDevOptResq wDataSize:[%d] < sizeof(CMD_BS_Mainboard_Dev_Opt_Resq)", wDataSize);
        return false;
    }
    CMD_BS_Mainboard_Dev_Opt_Resq *pResq = (CMD_BS_Mainboard_Dev_Opt_Resq *)pData;
    LOG_INFO("server.aibox", "OnMainBoardDevOptResq type:[%d] id:[%d] sta:[%d]", pResq->type, pResq->id, pResq->sta);
    auto pEnergyStorageCabin = m_EnergyStorageCabinManager.GetEnergyStorageCabinBySocketId(dwSocketID);
    if (pEnergyStorageCabin)
    {
        for (auto &it : pEnergyStorageCabin->m_pMainBoardInfo->m_vDevStatus)
        {
            if (it.id == pResq->id && it.type == pResq->type)
            {
                it.sta = pResq->sta;
                break;
            }
        }
    }

    // TODO通知客户端
    for (auto &client : m_UserInfoMap)
    {

        CMD_CS_Dev_Open_Resq clientDevOptResq;
        clientDevOptResq.uCabinId = pEnergyStorageCabin->m_sCode;
        clientDevOptResq.uTpye = pResq->type;
        clientDevOptResq.uOptType = 1;
        clientDevOptResq.uDevId = pResq->id;
        m_pITCPNetworkEngine->SendData(client.second->dwSocketID, MDM_CS_CLIENT, SUB_CS_CLIENT_DEV_OPEN_RESQ, &clientDevOptResq,
                                       sizeof(clientDevOptResq));
    }
    return true;
}

// bool CAttemperEngineSink::OnMainBoardDevOptResq(void *pData, uint16 wDataSize, uint32 dwSocketID)
// {
//     if (wDataSize < sizeof(CMD_BS_Mainboard_Dev_Opt_Resq))
//     {
//         LOG_INFO("server.aibox", "OnMainBoardDevOptResq wDataSize:[%d] < sizeof(CMD_BS_Mainboard_Dev_Opt_Resq)", wDataSize);
//         return false;
//     }
//     CMD_BS_Mainboard_Dev_Opt_Resq *pResq = (CMD_BS_Mainboard_Dev_Opt_Resq *)pData;
//     LOG_INFO("server.aibox", "OnMainBoardDevOptResq type:[%d] id:[%d] sta:[%d]", pResq->type, pResq->id, pResq->sta);
//     auto pEnergyStorageCabin = m_EnergyStorageCabinManager.GetEnergyStorageCabinBySocketId(dwSocketID);
//     if (pEnergyStorageCabin)
//     {
//         for(auto &it : pEnergyStorageCabin->m_pMainBoardInfo->m_vDevStatus)
//         {
//             if (it.id == pResq->id)
//             {
//                 it.sta = pResq->sta;
//                 break;
//             }
//         }

//         // TODO通知所有客户端 状态改变
//         for (auto &client : m_UserInfoMap)
//         {
//             CMD_CS_Dev_Open_Resq clientDevOptResq;
//             clientDevOptResq.uCabinId = pEnergyStorageCabin->m_uId;
//             clientDevOptResq.uTpye = pResq->type;
//             clientDevOptResq.uOptType = 1;//起瓶
//             clientDevOptResq.uDevId = pResq->id;
//             clientDevOptResq.uSta = pResq->sta;
//             m_pITCPNetworkEngine->SendData(client.second->dwSocketID, MDM_CS_CLIENT, SUB_SC_CLIENT_DEV_OPT_RESQ, &clientDevOptResq,
//             sizeof(clientDevOptResq));
//         }
//     }

//     return true;
// }

bool CAttemperEngineSink::OnMainBoardRegister(void *pData, uint16 wDataSize, uint32 dwSocketID)
{

    tagBindParameter *pBindParameter = (m_pBindParameter + dwSocketID);
    uint8 *pClientAddr = (uint8 *)&pBindParameter->dwClientAddr;
    std::string strClientIP = StringFormat("%d.%d.%d.%d", pClientAddr[3], pClientAddr[2], pClientAddr[1], pClientAddr[0]);
    LOG_INFO("server.aibox", "OnMainBoardRegister mainboardId:[%s]  success", strClientIP.c_str());

    InitEnergyStorageCabinByMainBoardId(strClientIP);
    auto pEnergyStorageCabin = m_EnergyStorageCabinManager.GetEnergyStorageCabinByMainBoardId(strClientIP);
    if (pEnergyStorageCabin)
    {
        if (!pEnergyStorageCabin->m_pMainBoardInfo)
        {
            pEnergyStorageCabin->m_pMainBoardInfo = std::make_shared<MainBoardInfo>();
        }
        pEnergyStorageCabin->m_pMainBoardInfo->m_nMainBoardId = strClientIP;
        pEnergyStorageCabin->m_pMainBoardInfo->m_nSocketId = dwSocketID;
        pEnergyStorageCabin->m_pMainBoardInfo->m_bIsOnline = true;
    }
    m_pITCPNetworkEngine->SendData(dwSocketID, MDM_BS_MAINBOARD, SUB_SB_MAINBOARD_REGISTER_RESQ, NULL, 0);

    m_pITimerEngine->SetTimer(IDI_MAINBOARD_STA, 3 * 1000, 1);
    return true;
}

bool CAttemperEngineSink::OnMainBoardStatus(void *pData, uint16 wDataSize, uint32 dwSocketID)
{
    if (wDataSize < sizeof(CMD_BS_Mainboard_Dev_Status))
    {
        LOG_INFO("server.aibox", "OnMainBoardStatus wDataSize:[%d] < sizeof(CMD_BS_Mainboard_Dev_Status)", wDataSize);
        return false;
    }

    CMD_BS_Mainboard_Dev_Status *pStatus = (CMD_BS_Mainboard_Dev_Status *)pData;
    LOG_INFO("server.aibox", "OnMainBoardStatus dev_cnt:[%d]", pStatus->dev_cnt);
    auto pEnergyStorageCabin = m_EnergyStorageCabinManager.GetEnergyStorageCabinBySocketId(dwSocketID);
    if (!pEnergyStorageCabin || !pEnergyStorageCabin->m_pMainBoardInfo)
    {
        LOG_INFO("server.aibox", "OnMainBoardStatus pEnergyStorageCabin is null");
        return false;
    }
    bool isChange = true;
    for (int j = 0; j < pStatus->dev_cnt; j++)
    {
        LOG_INFO("server.aibox", "OnMainBoardStatus type:[%d] id:[%d] sta:[%d] i:[%d]", pStatus->status[j].type, pStatus->status[j].id,
                 pStatus->status[j].sta, j);
        bool isFind = false;

        for (auto &it : pEnergyStorageCabin->m_pMainBoardInfo->m_vDevStatus)
        {
            // if (it.id == pStatus->status[j].id && pStatus->status[j].type == it.type && it.sta != pStatus->status[j].sta)
            // {
            //     isChange = true;
            // }
            if (it.id == pStatus->status[j].id && pStatus->status[j].type == it.type)
            {
                it.sta = pStatus->status[j].sta;
                isFind = true;
                break;
            }
        }
        if (!isFind)
        {
            pEnergyStorageCabin->m_pMainBoardInfo->m_vDevStatus.push_back(pStatus->status[j]);
        }
        // auto now = time(NULL);
        // if (m_now != 0 && now - m_now > 15)
        // {
        //     if (pStatus->status[j].type == 2 && (pStatus->status[j].sta == 1 || pStatus->status[j].sta == 2) && !m_onLine)
        //     {
        //         CMD_BS_Mainboard_Dev_Online devOpt;
        //         devOpt.type = 2;
        //         devOpt.id = 1;
        //         m_pITCPNetworkEngine->SendData(pEnergyStorageCabin->m_pMainBoardInfo->m_nSocketId, MDM_BS_MAINBOARD, SUB_SB_MAINBOARD_DEV_ONLINE,
        //                                        &devOpt, sizeof(CMD_BS_Mainboard_Dev_Online));
        //         m_onLine = true;
        //     }
        // }
    }

    // 有变化就通知客户端
    if (isChange)
    {
        for (auto &client : m_UserInfoMap)
        {
            OnClientCabinSta(NULL, 0, client.second->dwSocketID);
            // m_pITCPNetworkEngine->SendData(client.second->dwSocketID, MDM_CS_CLIENT, SUB_SC_CLIENT_TEMP, &clientTemp, sizeof(clientTemp));
        }
    }
    // if (!m_nOpt)
    // {

    //     CMD_BS_Mainboard_Dev_Opt devOpt;
    //     devOpt.type = 2;
    //     devOpt.id = 1;
    //     m_pITCPNetworkEngine->SendData(pEnergyStorageCabin->m_pMainBoardInfo->m_nSocketId, MDM_BS_MAINBOARD, SUB_SB_MAINBOARD_DEV_OPT, &devOpt,
    //                                    sizeof(CMD_BS_Mainboard_Dev_Online));
    //     m_nOpt = true;
    //     m_now = time(NULL);
    // }

    return true;
}

bool CAttemperEngineSink::OnClientDevOpen(void *pData, uint16 wDataSize, uint32 dwSocketID)
{
    if (wDataSize < sizeof(CMD_CS_Dev_Open))
    {
        LOG_INFO("server.aibox", "OnClientDevOpen wDataSize:[%d] < sizeof(CMD_CS_Dev_Open)", wDataSize);
        return false;
    }
    CMD_CS_Dev_Open *pOpen = (CMD_CS_Dev_Open *)pData;
    LOG_INFO("server.aibox", "OnClientDevOpen uCabinId:[%d] uTpye:[%d] uDevId:[%d] uOptType:[%d]", pOpen->uCabinId, pOpen->uTpye, pOpen->uDevId,
             pOpen->uOptType);
    auto pEnergyStorageCabin = m_EnergyStorageCabinManager.GetEnergyStorageCabinByCode(pOpen->uCabinId);
    if (pEnergyStorageCabin)
    {
        if (pEnergyStorageCabin->m_pMainBoardInfo)
        {
            // pEnergyStorageCabin->m_pMainBoardInfo->m_socket;
            if (pOpen->uOptType == 2)//上线
            {
                CMD_BS_Mainboard_Dev_Online devOpt;
                devOpt.type = pOpen->uTpye;
                devOpt.id = pOpen->uDevId;
                m_pITCPNetworkEngine->SendData(pEnergyStorageCabin->m_pMainBoardInfo->m_nSocketId, MDM_BS_MAINBOARD, SUB_SB_MAINBOARD_DEV_ONLINE,
                                               &devOpt, sizeof(CMD_BS_Mainboard_Dev_Online));
            }
            else
            {
                CMD_BS_Mainboard_Dev_Opt devOpt;
                devOpt.type = pOpen->uTpye;
                devOpt.id = pOpen->uDevId;
                m_pITCPNetworkEngine->SendData(pEnergyStorageCabin->m_pMainBoardInfo->m_nSocketId, MDM_BS_MAINBOARD, SUB_SB_MAINBOARD_DEV_OPT,
                                               &devOpt, sizeof(CMD_BS_Mainboard_Dev_Opt));
            }

            return true;
        }
    }
    LOG_INFO("server.aibox", "OnClientDevOpen 没有找到仓储 uCabinId:[%d] ", pOpen->uCabinId);
    return true;
}

bool CAttemperEngineSink::OnClientQueryMsg(void *pData, uint16 wDataSize, uint32 dwSocketID)
{

    CMD_SC_Dev_Statistics statistics;

    std::string alarmSql = "select type, count(*) from alarm_log_table   group by type;";
    DB::QueryResult query = DB::LogonDatabasePool.Query(alarmSql.c_str());
    if (query)
    {
        do
        {
            DB::Field *field = query->Fetch();
            int type = field[0].GetInt32();
            int count = field[1].GetInt32();
            if (type == ALARM_TYPE_PERSONNEL)
            {
                statistics.stWarnCnt.u16PersonCnt = count;
            }
            else if (type == ALARM_TYPE_TEMP)
            {
                statistics.stWarnCnt.u16TemperCnt = count;
            }
            else if (type == ALARM_TYPE_FIRE)
            {
                statistics.stWarnCnt.u16FireCnt = count;
            }
        } while (query->NextRow());
    }

    std::string aidevSql = "SELECT status, COUNT(*) FROM camera_info_table GROUP BY status";
    DB::QueryResult devQuery = DB::LogonDatabasePool.Query(aidevSql.c_str());
    if (devQuery)
    {
        do
        {
            DB::Field *field = devQuery->Fetch();
            int status = field[0].GetInt32();
            int count = field[1].GetInt32();

            switch (status)
            {
            case CAMERA_STATUS_NORMAL:
                statistics.stAiDev.u16NormalCnt = count;
                break;
            case CAMERA_STATUS_ALARM:
                statistics.stAiDev.u16AlarmCnt = count;
                break;
            case CAMERA_STATUS_ERROR:
                statistics.stAiDev.u16ErrlCnt = count;
                break;
            }
        } while (devQuery->NextRow());
    }

    std::string aiboxSql = "SELECT id, camera_list FROM ai_box_table ";
    DB::QueryResult boxQuery = DB::LogonDatabasePool.Query(aiboxSql.c_str());
    if (boxQuery)
    {
        do
        {
            DB::Field *field = boxQuery->Fetch();
            int id = field[0].GetInt32();
            std::string cameraList = field[1].GetString();
            std::vector<std::string> cameraIdList;
            Util::StringUtility::Split(cameraList, ",", &cameraIdList);
            for (auto &it : cameraIdList)
            {
                int cameraId = std::stoi(it);
                std::string cameraSql = fmt::format("SELECT status FROM camera_info_table WHERE id = {}", cameraId);
                DB::QueryResult cameraQuery = DB::LogonDatabasePool.Query(cameraSql.c_str());
                if (cameraQuery)
                {
                    DB::Field *field = cameraQuery->Fetch();
                    int status = field[0].GetInt32();
                    if (status == CAMERA_STATUS_ALARM)
                    {
                        statistics.stBoxDev.u16AlarmCnt += 1;
                        continue;
                    }
                    if (m_BoxListManager.IsContainBox(cameraId))
                    {
                        statistics.stHuiYanDev.u16NormalCnt += 1;
                    }
                    else
                    {
                        statistics.stBoxDev.u16ErrlCnt += 1;
                    }
                }
            }
        } while (boxQuery->NextRow());
    }

    std::string cameraSql = "SELECT status, COUNT(*) FROM camera_info_table where device_location_type = 1 GROUP BY status";
    DB::QueryResult cameraQuery = DB::LogonDatabasePool.Query(cameraSql.c_str());
    if (cameraQuery)
    {
        do
        {
            DB::Field *field = cameraQuery->Fetch();
            int status = field[0].GetInt32();
            int count = field[1].GetInt32();
            switch (status)
            {
            case CAMERA_STATUS_NORMAL:
                statistics.stHuiYanDev.u16NormalCnt = count;
                break;
            case CAMERA_STATUS_ALARM:
                statistics.stHuiYanDev.u16AlarmCnt = count;
                break;
            case CAMERA_STATUS_ERROR:
                statistics.stHuiYanDev.u16ErrlCnt = count;
                break;
            }
        } while (cameraQuery->NextRow());
    }

    std::string levelSql = "SELECT level, COUNT(*) FROM alarm_log_table GROUP BY level";
    DB::QueryResult levelQuery = DB::LogonDatabasePool.Query(levelSql.c_str());
    if (levelQuery)
    {
        do
        {
            DB::Field *field = levelQuery->Fetch();
            int level = field[0].GetInt32();
            int count = field[1].GetInt32();
            switch (level)
            {
            case 1:
                statistics.stAlarmLevenCnt.u16Level1Cnt = count;
                break;
            case 2:
                statistics.stAlarmLevenCnt.u16Level2Cnt = count;
                break;
            case 3:
                statistics.stAlarmLevenCnt.u16Level3Cnt = count;
                break;
            }
        } while (levelQuery->NextRow());
    }

    m_pITCPNetworkEngine->SendData(dwSocketID, MDM_CS_CLIENT, SUB_SC_CLIENT_DEV_STA, &statistics, sizeof(statistics));

    return true;
}

bool CAttemperEngineSink::OnClientRegister(void *pData, uint16 wDataSize, uint32 dwSocketID)
{
    if (wDataSize < sizeof(CMD_CS_ClientRegister))
    {
        LOG_INFO("server.aibox", "OnClientRegister wDataSize:[%d] < sizeof(CMD_CS_ClientRegister)", wDataSize);
        return false;
    }
    CMD_CS_ClientRegister *pRegister = (CMD_CS_ClientRegister *)pData;
    LOG_INFO("server.aibox", "OnClientRegister name:[%s] pwd:[%s] success", pRegister->cName, pRegister->cPWD);
    std::string sql = fmt::format("select * from client_table where `name` = '{}' and `pwd` = '{}' ", pRegister->cName, pRegister->cPWD);
    DB::QueryResult result = DB::LogonDatabasePool.Query(sql.c_str());
    if (result)
    {
        UserInfoPtr userInfoPtr = std::make_shared<UserInfo>();
        userInfoPtr->name = pRegister->cName;
        userInfoPtr->pwd = pRegister->cPWD;
        userInfoPtr->dwSocketID = dwSocketID;
        m_UserInfoMap.insert(std::make_pair(dwSocketID, userInfoPtr));

        CMD_SC_ClientRegisterResq clientRegisterResq;
        clientRegisterResq.dwUserRight = 1;
        m_pITCPNetworkEngine->SendData(dwSocketID, MDM_CS_CLIENT, SUB_SC_CLIENT_REGISTER_RESQ, &clientRegisterResq, sizeof(clientRegisterResq));
    }

    return true;
}

bool CAttemperEngineSink::OnBoxTempEvent(uint16 wSubCmdID, void *pData, uint16 wDataSize, uint32 dwSocketID)
{
    switch (wSubCmdID)
    {
    case SUB_CS_TEMP_WARN: // ������Ϣ
    {
        return OnTCPNetworkSubTempWarn(pData, wDataSize, dwSocketID);
    }
    case SUB_CS_TEMP_INFO: // �¶�
    {
        return OnTCPNetworkSubTempInfo(pData, wDataSize, dwSocketID);
    }
    }
    return false;

    return false;
}
bool CAttemperEngineSink::OnBoxRegister(void *pData, uint16 wDataSize, uint32 dwSocketID)
{
    if (wDataSize < sizeof(TCP_block))
    {
        return false;
    }

    std::string jdata((char *)(pData + sizeof(TCP_block)), wDataSize - sizeof(TCP_block));
    CMD_CS_BoxRegister boxRegister;

    try
    {
        xpack::json::decode(jdata, boxRegister);
        LOG_INFO("server.aibox", "OnBoxRegister uBoxId:[%d]", boxRegister.uBoxId);

        // ��ȡ��Ե����е����?
        std::string sql = fmt::format("SELECT "
                                      "`id`,`ip`,`device_location_id`,`alg_type`,`camera_"
                                      "list`,`desc` FROM ai_box_table WHERE `id` = {} ",
                                      boxRegister.uBoxId);
        DB::QueryResult result = DB::LogonDatabasePool.Query(sql.c_str());
        if (result)
        {

            BoxPtr pBoxInfo = std::make_shared<AIBoxInfo>();
            auto field = result->Fetch();
            int index = 0;
            pBoxInfo->m_nBoxId = field[index++].GetInt32();
            pBoxInfo->m_sIp = field[index++].GetString();
            pBoxInfo->m_nDeviceLocationId = field[index++].GetInt32();
            pBoxInfo->m_nAlgType = field[index++].GetInt32();
            std::string sCameraList = field[index++].GetString();
            pBoxInfo->m_sDesc = field[index++].GetString();

            pBoxInfo->m_nSocketId = dwSocketID;
            if (!sCameraList.empty())
            {

                // ��ȡ�����Ƶ�����ͷ��Ϣ
                std::string cameraSql = fmt::format(" select `id`,`ip`,`stream_url`, `user` , "
                                                    "`pwd`,`name`,`push_url`,`format`,`device_id`,`status`,`"
                                                    "device_location_type` ,`device_location_id` "
                                                    "from  camera_info_table WHERE `id` IN ( {} )",
                                                    sCameraList);
                DB::QueryResult cameraResult = DB::LogonDatabasePool.Query(cameraSql.c_str());
                CameraInfolist cameraList;

                do
                {
                    DB::Field *field = cameraResult->Fetch();

                    CameraInfo cameraInfo;
                    int index = 0;
                    cameraInfo.ID = field[index++].GetInt32();
                    cameraInfo.IP = field[index++].GetString();
                    cameraInfo.RTSP = field[index++].GetString();
                    cameraInfo.username = field[index++].GetString();
                    cameraInfo.password = field[index++].GetString();
                    cameraInfo.istemp = false;
                    if (pBoxInfo->m_nAlgType == EnergyStorageCabin)
                    {
                        cameraInfo.istemp = true;
                    }

                    cameraList.CameraInfoList.push_back(cameraInfo);

                    //
                    CameraDevInfoPtr cameraDevInfoPtr = std::make_shared<CameraDevInfo>();
                    cameraDevInfoPtr->m_nId = cameraInfo.ID;
                    cameraDevInfoPtr->m_sIp = cameraInfo.IP;
                    cameraDevInfoPtr->m_sStreamUrl = cameraInfo.RTSP;
                    cameraDevInfoPtr->m_sUser = cameraInfo.username;
                    cameraDevInfoPtr->m_sPwd = cameraInfo.password;
                    cameraDevInfoPtr->m_sName = field[index++].GetString();
                    cameraDevInfoPtr->m_sPushUrl = field[index++].GetString();
                    cameraDevInfoPtr->m_sFormat = field[index++].GetString();
                    cameraDevInfoPtr->m_nDevice_id = field[index++].GetInt32();
                    cameraDevInfoPtr->m_nStatus = field[index++].GetInt32();
                    cameraDevInfoPtr->m_nLocationType = field[index++].GetInt32();
                    cameraDevInfoPtr->m_nLocationId = field[index++].GetInt32();

                    pBoxInfo->m_vCameraList.push_back(cameraDevInfoPtr);

                    // ��ʼ�����ܲ�����
                    std::cout << "m_nLocationType:" << cameraDevInfoPtr->m_nLocationType << std::endl;
                    if (cameraDevInfoPtr->m_nLocationType == EnergyStorageCabin)
                    {
                        InitEnergyStorageCabin(cameraDevInfoPtr->m_nLocationId);
                    }

                } while (cameraResult->NextRow());

                std::string json = xpack::json::encode(cameraList);
                // LOG_INFO("server.aibox", "OnBoxRegister camera list:[%s]",
                // json.c_str());
                TCP_block block;
                block.nblockcount = 1;
                block.nblockid = 1;
                block.nblockGUID = Util::generate_compressed_uuid();
                char buff[1024 * 10] = {0};
                memcpy(buff, &block, sizeof(block));
                memcpy(buff + sizeof(block), json.data(), json.size());
                std::string strData = std::string(buff, sizeof(block) + json.size());

                // ��������
                m_pITCPNetworkEngine->SendData(pBoxInfo->m_nSocketId, MDM_CS_CAMERA, SUB_CS_CAMERA_INFO, (void *)strData.data(), strData.size());

                LOG_INFO("server.aibox", "OnBoxRegister camera info:[%s]", json.c_str());

                // ��������ͷ�㷨
                if (pBoxInfo->m_nAlgType == 2)
                { // 算法类型：1围栏跨界算法 2火焰
                    SendCameraAglConfig(dwSocketID, boxRegister.uBoxId, sCameraList);
                }
                else
                {
                    SendWaiWeiCameraAglConfig(dwSocketID, boxRegister.uBoxId, sCameraList);
                }
                m_BoxListManager.AddAIBoxInfo(pBoxInfo);
            }
        }
    }
    catch (const std::exception &e)
    {
        LOG_WARN("server.aibox", "OnBoxRegister err:[%s]", e.what());
    }

    return true;
}

void CAttemperEngineSink::InitEnergyStorageCabin(uint32 cabinId)
{
    if (m_EnergyStorageCabinManager.IsContain(cabinId))
    {
        return;
    }

    std::string sql = fmt::format("SELECT `id`,battery_power,`rated_capacity_kwh`,`operating_status`,`temp`, `mainboardid` , code FROM "
                                  "energy_storage_cabin_table WHERE `id` = {} ",
                                  cabinId);
    DB::QueryResult result = DB::LogonDatabasePool.Query(sql.c_str());
    if (result)
    {
        DB::Field *field = result->Fetch();
        int index = 0;
        EnergyStorageCabinPtr pEnergyStorageCabin = std::make_shared<EnergyStorageCabinInfo>();
        pEnergyStorageCabin->m_uId = field[index++].GetInt32();
        pEnergyStorageCabin->m_uBatteryPower = field[index++].GetInt32();
        pEnergyStorageCabin->m_uRatedCapacityKwh = field[index++].GetInt32();
        pEnergyStorageCabin->m_uOperatingStatus = field[index++].GetInt32();
        pEnergyStorageCabin->m_uTemp = field[index++].GetInt32();
        pEnergyStorageCabin->m_nMainBoardId = field[index++].GetString();
        pEnergyStorageCabin->m_sCode = field[index++].GetInt32();

        m_EnergyStorageCabinManager.AddEnergyStorageCabin(pEnergyStorageCabin);

        // ת��ǰ��
    }
}

void CAttemperEngineSink::InitEnergyStorageCabinByMainBoardId(const std::string &mainboardIp)
{
    if (m_EnergyStorageCabinManager.IsContainByMainBoardId(mainboardIp))
    {
        return;
    }

    std::string sql = fmt::format(" SELECT `id`,battery_power,`rated_capacity_kwh`,`operating_status`,`temp`, `mainboardid`,code  FROM "
                                  "energy_storage_cabin_table WHERE `mainboardid` = \"{}\" ",
                                  mainboardIp);
    DB::QueryResult result = DB::LogonDatabasePool.Query(sql.c_str());
    if (result)
    {
        DB::Field *field = result->Fetch();
        int index = 0;
        EnergyStorageCabinPtr pEnergyStorageCabin = std::make_shared<EnergyStorageCabinInfo>();
        pEnergyStorageCabin->m_uId = field[index++].GetInt32();
        pEnergyStorageCabin->m_uBatteryPower = field[index++].GetInt32();
        pEnergyStorageCabin->m_uRatedCapacityKwh = field[index++].GetInt32();
        pEnergyStorageCabin->m_uOperatingStatus = field[index++].GetInt32();
        pEnergyStorageCabin->m_uTemp = field[index++].GetInt32();
        pEnergyStorageCabin->m_nMainBoardId = field[index++].GetString();
        pEnergyStorageCabin->m_sCode = field[index++].GetInt32();

        m_EnergyStorageCabinManager.AddEnergyStorageCabin(pEnergyStorageCabin);
    }
}

void CAttemperEngineSink::SendWaiWeiCameraAglConfig(uint32 dwSocketID, uint32 boxId, std::string &sCameraList)
{

    std::string cameraSql = fmt::format("SELECT `camera_id`,`alg_id`,`cycle`, "
                                        "`dp`,`dp_id`,`interval`,`hasTimes`,`level`, "
                                        "`people_count`,`thrs`,`vehicletype`,`vehs`,`lines`,`"
                                        "polygons`, `width`, `height`,`wb` FROM "
                                        "camera_alg_conf where `camera_id` in ( {} ) ;",
                                        sCameraList);
    sParam_idlist cameraParamList;
    auto query1 = DB::LogonDatabasePool.Query(cameraSql.c_str());
    std::vector<std::string> vCameraIdList;
    try
    {
        Util::StringUtility::Split(sCameraList, ",", &vCameraIdList);
    }
    catch (const std::exception &e)
    {
        LOG_INFO("server.aibox", "SendCameraAglConfig Split err:[%s]", e.what());
        return;
    }

    std::string jsonErr = "";
    if (query1)
    {
        do
        {
            auto field = query1->Fetch();
            // camera_id`,`alg_id`,`cycle`,
            // `dp`,`dp_id`,`interval`,`hasTimes`,`level`, "
            //"`people_count`,`thrs`,`vehicletype`,`vehs`,`lines`,`polygons`,
            //`width`, `height`,`wb`
            int index = 0;
            int camera_id = field[index++].GetInt32();
            int alg_id = field[index++].GetInt32();
            int cycle = field[index++].GetInt32();
            int dp = field[index++].GetInt32();
            int dp_id = field[index++].GetInt32();
            int interval = field[index++].GetInt32();
            int hasTimes = field[index++].GetInt32();
            int level = field[index++].GetInt32();
            int people_count = field[index++].GetInt32();
            std::string thrs = field[index++].GetString();
            std::string vehicletype = field[index++].GetString();
            std::string vehs = field[index++].GetString();
            std::string lines = field[index++].GetString();
            std::string polygons = field[index++].GetString();
            int width = field[index++].GetInt32();
            int height = field[index++].GetInt32();
            int wb = field[index++].GetInt32();

            sparam_id paramId;
            // int camera_id = std::stoi(sCameraId);
            paramId.cameraid = camera_id;
            sParam param;
            try
            {

                jsonErr.clear();
                // thrs
                Json::Value jVal = Util::stringToJson(thrs, jsonErr);
                if (jVal.isArray())
                {
                    for (auto item : jVal)
                    {
                        param.thrs.push_back(item.asFloat());
                    }
                }

                // lines
                // auto algMap = lineMap.find(camera_id);
                // if (algMap != lineMap.end()) {
                //     auto lineVec = algMap->second.find(alg_id);
                //     if (lineVec != algMap->second.end()) {
                //         param.lines.insert(param.lines.end(),
                //         lineVec->second.begin(), lineVec->second.end());
                //     }
                // }

                // polygons
                jsonErr.clear();
                Json::Value polygonsVal = Util::stringToJson(polygons, jsonErr);
                if (jsonErr.empty())
                {
                    for (auto jv : polygonsVal)
                    {
                        auto polygonsValList = jv["points"];
                        std::vector<spoint> vec;
                        if (polygonsValList.isArray())
                        {
                            for (auto v : polygonsValList)
                            {

                                spoint p;
                                p.x = v["x"].asFloat();
                                p.y = v["y"].asFloat();

                                vec.emplace_back(p);
                            }
                        }
                        if (vec.size() > 0)
                        {
                            param.mask_points.emplace_back(vec);
                        }
                    }
                }

                // param.mask_points
                param.Post_person = people_count;

                param.warn_interval = interval;
                param.warn_thr = hasTimes;
                param.warn_window = cycle;
                param.warn_level = level;

                param.algid = (algtype)alg_id;
            }
            catch (const std::exception &e)
            {
                std::cerr << e.what() << '\n';
            }

            bool find = false;
            for (auto &paramList : cameraParamList.algparamidList)
            {
                if (paramList.cameraid == camera_id)
                {
                    paramList.param.emplace_back(param);
                    find = true;
                }
            }
            if (!find)
            {
                paramId.param.emplace_back(param);
                cameraParamList.algparamidList.emplace_back(paramId);
            }

        } while (query1->NextRow());
    }

    std::string cameraconfigParam = xpack::json::encode(cameraParamList);
    int sendmax = 1024 * 10 - sizeof(TCP_block);
    int eleSize = cameraconfigParam.size();
    TCP_block block;
    block.nblockGUID = 999; // Util::generate_compressed_uuid();
    if (cameraconfigParam.size() > sendmax)
    {
        block.nblockcount = 1;
        if (cameraconfigParam.size() % sendmax == 0)
        {
            block.nblockcount = cameraconfigParam.size() / sendmax;
            eleSize = sendmax;
        }
        else
        {
            block.nblockcount = cameraconfigParam.size() / sendmax + 1;
            eleSize = sendmax;
        }
    }
    for (int i = 0; i < block.nblockcount; ++i)
    {
        // block.nblockcount = 1;
        block.nblockid = i + 1;
        char buff[1024 * 10] = {0};
        int temp = sendmax;
        if (cameraconfigParam.size() - (i * sendmax) < sendmax)
        {
            temp = cameraconfigParam.size() - (i * sendmax);
        }
        memcpy(buff, &block, sizeof(block));
        memcpy(buff + sizeof(block), cameraconfigParam.data() + i * sendmax, temp);
        std::string strData = std::string(buff, sizeof(block) + temp);

        LOG_INFO("server.aibox", "SendData camera alg [%s]", cameraconfigParam.c_str());
        // std::cout << "block.nblockid:" << block.nblockid << " temp:" << temp
        // << std::endl;
        m_pITCPNetworkEngine->SendData(dwSocketID, MDM_CS_ALG, SUB_CS_ALG_INFO, (void *)strData.data(), strData.size());
    }
}

void CAttemperEngineSink::SendCameraAglConfig(uint32 dwSocketID, uint32 boxId, std::string &sCameraList)
{
    // ��������ͷ�㷨
    std::string cameraSql = fmt::format("SELECT `box_id`,`alg_id`,`cycle`, "
                                        "`dp`,`dp_id`,`interval`,`hasTimes`,`level`, "
                                        "`people_count`,`thrs`,`vehicletype`,`vehs`,`lines`,`"
                                        "polygons`, `width`, `height`,`wb` FROM "
                                        "aibox_alg_conf where `box_id` in ( {} ) ;",
                                        boxId);
    sParam_idlist cameraParamList;
    auto query1 = DB::LogonDatabasePool.Query(cameraSql.c_str());
    std::vector<std::string> vCameraIdList;
    try
    {
        Util::StringUtility::Split(sCameraList, ",", &vCameraIdList);
    }
    catch (const std::exception &e)
    {
        LOG_INFO("server.aibox", "SendCameraAglConfig Split err:[%s]", e.what());
        return;
    }

    std::string jsonErr = "";
    if (query1)
    {
        do
        {
            auto field = query1->Fetch();
            // camera_id`,`alg_id`,`cycle`,
            // `dp`,`dp_id`,`interval`,`hasTimes`,`level`, "
            //"`people_count`,`thrs`,`vehicletype`,`vehs`,`lines`,`polygons`,
            //`width`, `height`,`wb`
            int index = 0;
            int aiboxId = field[index++].GetInt32();
            int alg_id = field[index++].GetInt32();
            int cycle = field[index++].GetInt32();
            int dp = field[index++].GetInt32();
            int dp_id = field[index++].GetInt32();
            int interval = field[index++].GetInt32();
            int hasTimes = field[index++].GetInt32();
            int level = field[index++].GetInt32();
            int people_count = field[index++].GetInt32();
            std::string thrs = field[index++].GetString();
            std::string vehicletype = field[index++].GetString();
            std::string vehs = field[index++].GetString();
            std::string lines = field[index++].GetString();
            std::string polygons = field[index++].GetString();
            int width = field[index++].GetInt32();
            int height = field[index++].GetInt32();
            int wb = field[index++].GetInt32();

            for (auto sCameraId : vCameraIdList)
            {
                sparam_id paramId;
                int camera_id = std::stoi(sCameraId);
                paramId.cameraid = camera_id;
                sParam param;
                try
                {

                    jsonErr.clear();
                    // thrs
                    Json::Value jVal = Util::stringToJson(thrs, jsonErr);
                    if (jVal.isArray())
                    {
                        for (auto item : jVal)
                        {
                            param.thrs.push_back(item.asFloat());
                        }
                    }

                    // lines
                    // auto algMap = lineMap.find(camera_id);
                    // if (algMap != lineMap.end()) {
                    //     auto lineVec = algMap->second.find(alg_id);
                    //     if (lineVec != algMap->second.end()) {
                    //         param.lines.insert(param.lines.end(),
                    //         lineVec->second.begin(), lineVec->second.end());
                    //     }
                    // }

                    // polygons
                    jsonErr.clear();
                    Json::Value polygonsVal = Util::stringToJson(polygons, jsonErr);
                    if (jsonErr.empty())
                    {
                        for (auto jv : polygonsVal)
                        {
                            auto polygonsValList = jv["points"];
                            std::vector<spoint> vec;
                            if (polygonsValList.isArray())
                            {
                                for (auto v : polygonsVal)
                                {

                                    spoint p;
                                    p.x = v["x"].asFloat();
                                    p.y = v["y"].asFloat();

                                    vec.emplace_back(p);
                                }
                            }
                            if (vec.size() > 0)
                            {
                                param.mask_points.emplace_back(vec);
                            }
                        }
                    }

                    // param.mask_points
                    param.Post_person = people_count;

                    param.warn_interval = interval;
                    param.warn_thr = hasTimes;
                    param.warn_window = cycle;
                    param.warn_level = level;

                    param.algid = (algtype)alg_id;
                }
                catch (const std::exception &e)
                {
                    std::cerr << e.what() << '\n';
                }

                bool find = false;
                for (auto &paramList : cameraParamList.algparamidList)
                {
                    if (paramList.cameraid == camera_id)
                    {
                        paramList.param.emplace_back(param);
                        find = true;
                    }
                }
                if (!find)
                {
                    paramId.param.emplace_back(param);
                    cameraParamList.algparamidList.emplace_back(paramId);
                }
            }

        } while (query1->NextRow());
    }

    std::string cameraconfigParam = xpack::json::encode(cameraParamList);
    int sendmax = 1024 * 10 - sizeof(TCP_block);
    int eleSize = cameraconfigParam.size();
    TCP_block block;
    block.nblockGUID = 999; // Util::generate_compressed_uuid();
    if (cameraconfigParam.size() > sendmax)
    {
        block.nblockcount = 1;
        if (cameraconfigParam.size() % sendmax == 0)
        {
            block.nblockcount = cameraconfigParam.size() / sendmax;
            eleSize = sendmax;
        }
        else
        {
            block.nblockcount = cameraconfigParam.size() / sendmax + 1;
            eleSize = sendmax;
        }
    }
    for (int i = 0; i < block.nblockcount; ++i)
    {
        // block.nblockcount = 1;
        block.nblockid = i + 1;
        char buff[1024 * 10] = {0};
        int temp = sendmax;
        if (cameraconfigParam.size() - (i * sendmax) < sendmax)
        {
            temp = cameraconfigParam.size() - (i * sendmax);
        }
        memcpy(buff, &block, sizeof(block));
        memcpy(buff + sizeof(block), cameraconfigParam.data() + i * sendmax, temp);
        std::string strData = std::string(buff, sizeof(block) + temp);

        LOG_INFO("server.aibox", "SendData camera alg [%s]", cameraconfigParam.c_str());
        // std::cout << "block.nblockid:" << block.nblockid << " temp:" << temp
        // << std::endl;
        m_pITCPNetworkEngine->SendData(dwSocketID, MDM_CS_ALG, SUB_CS_ALG_INFO, (void *)strData.data(), strData.size());
    }
}

void print_stack_info()
{
    pthread_attr_t attr;
    void *stack_addr;
    size_t stack_size;

    pthread_getattr_np(pthread_self(), &attr);
    pthread_attr_getstack(&attr, &stack_addr, &stack_size);

    // ��������ջ�ռ�
    volatile char dummy;
    size_t used = stack_size - (&dummy - (char *)stack_addr);

    std::cout << "ջ�ܴ�С: " << stack_size << " �ֽ�\n"
              << "����ջ�ռ�: " << used << " �ֽ�\n"
              << "ʣ��ջ�ռ�: " << (stack_size - used) << " �ֽ�\n";

    pthread_attr_destroy(&attr);
}

bool CAttemperEngineSink::saveImageToFile(const std::vector<unsigned char> &imgbuffer, const std::string &filename)
{
    std::ofstream outfile(filename, std::ios::binary);
    if (!outfile)
    {
        return false;
    }

    outfile.write(reinterpret_cast<const char *>(imgbuffer.data()), imgbuffer.size());
    return outfile.good();
}

bool CAttemperEngineSink::OnTCPNetworkSubCSImage(void *pData, uint16 wDataSize, uint32 dwSocketID)
{
    TCP_block *p = (TCP_block *)pData;
    // print_stack_info();
    std::cout << "=======OnTCPNetworkSubCSImage=========:" << this->data << "  currentSize:" << wDataSize - sizeof(TCP_block) << std::endl;
    {
        std::lock_guard<std::mutex> lock(m_mapMut);

        int realSize = wDataSize - sizeof(TCP_block);
        auto uuid = p->nblockGUID;
        std::cout << "======uuid======:" << uuid << std::endl;
        std::shared_ptr<MsgInfo> msgPtr = nullptr;
        if (p->nblockid == 1)
        {
            msgPtr = std::make_shared<MsgInfo>();
            msgPtr->msg;
            memset(msgPtr->msg, 0, sizeof(msgPtr->msg));
            // memset(this->img, 0, sizeof(this->img));
            if (realSize > sizeof(msgPtr->msg))
            {
                memcpy(msgPtr->msg, pData + sizeof(TCP_block), sizeof(msgPtr->msg));
            }
            else
            {
                memcpy(msgPtr->msg, pData + sizeof(TCP_block), realSize);
            }
            m_msgPool[uuid] = msgPtr;
        }
        else
        {
            if (m_msgPool.find(uuid) != m_msgPool.end())
            {
                msgPtr = m_msgPool.find(uuid)->second;
                int leftSize = Data_Size - msgPtr->count;
                if (realSize > leftSize)
                {
                    memcpy(msgPtr->msg + msgPtr->count, pData + sizeof(TCP_block), leftSize);
                }
                else
                {
                    memcpy(msgPtr->msg + msgPtr->count, pData + sizeof(TCP_block), realSize);
                }
            }
        }
        if (msgPtr)
        {
            msgPtr->count += realSize;
        }

        // this->data += realSize;
        if (p->nblockcount == p->nblockid)
        {
            /* code */
            if (msgPtr)
            {
                std::string jdata(msgPtr->msg, sizeof(msgPtr->msg));
                sWarn warn;
                // LOG_INFO("server.logon", "-------------------jdata:[%s]",
                // jdata.c_str());
                try
                {
                    xpack::json::decode(jdata, warn);
                    std::cout << "=======OnTCPNetworkSubCSImage======1111111===algid:" << warn.algid << " cameraid : " << warn.cameraid
                              << " level : " << warn.level << " m_nTime : " << warn.m_nTime << std::endl;

                    // дд�ļ�
                    // XGlobal_Get(CDirManager, pDir, Dir);
                    // std::string dateStr = Util::getCurrentTimeDayString();
                    // std::string timeStr = Util::getCurrentTimeHmssString();

                    // std::string imgPath =
                    //     fmt::format("{}/{}/{}",
                    //     pDir->getPath(CDirManager::AlarmPath), warn.cameraid,
                    //     dateStr);

                    // CTools::checkPath(imgPath);

                    // std::string imgFile = fmt::format("{}/{}.jpg", imgPath,
                    // timeStr);

                    // saveImageToFile(warn.imgbuffer, imgFile);
                    // std::string img_file = fmt::format("{}/{}/{}/{}.jpg",
                    // pDir->getSubPath(CDirManager::AlarmPath),
                    //                                    warn.cameraid,
                    //                                    dateStr, timeStr);
                    std::cout << "========imgpath===========:" << warn.imgpath << std::endl;
                    auto stmt = DB::LogonDatabasePool.GetPreparedStatement(AI_BOX_CAMERA_ALARM_LOG_INSERT);
                    CameraDevInfoPtr pCameraInfo;
                    m_BoxListManager.FindCameraByCameraId(warn.cameraid, pCameraInfo);
                    stmt->SetInt32(0, warn.cameraid);
                    if (pCameraInfo)
                    {
                        stmt->SetString(1, pCameraInfo->m_sName);
                        stmt->SetString(2, std::to_string(pCameraInfo->m_nDevice_id));
                        stmt->SetInt32(3, pCameraInfo->m_nLocationId);
                        stmt->SetInt32(4, (int)EnergyStorageCabin);
                    }

                    auto ses = secondsSinceMidnight();

                    stmt->SetInt64(5, warn.m_nTime);
                    uint8_t nWarnType = ALARM_TYPE_FIRE;
                    if (warn.algid == 1006)
                    {
                        nWarnType = ALARM_TYPE_PERSONNEL;
                    }

                    BoxPtr pAIBoxInfo = m_BoxListManager.GetAIBoxInfo(dwSocketID);
                    if (pAIBoxInfo)
                    {
                        warn.imgpath = "\\\\" + pAIBoxInfo->m_sIp + warn.imgpath;
                    }
                    Util::StringUtility::string_replace("/", "\\", &warn.imgpath);

                    stmt->SetInt64(6, nWarnType);

                    stmt->SetInt64(7, nWarnType);
                    stmt->SetString(8, warn.imgpath);
                    stmt->SetInt32(9, 0);

                    DB::LogonDatabasePool.Query(stmt);

                    NoticeClientWarning(warn.cameraid, 0, warn.m_nTime, nWarnType, warn.imgpath);
                }
                catch (const std::exception &e)
                {
                    std::cerr << e.what() << '\n';
                }
                m_msgPool.erase(uuid);
            }

            // std::string jdata(this->img, sizeof(this->img));
            // // LOG_INFO("server.logon", "Host:[%s] Port:[%d]
            // ThreadCount:[%d]", strBindIP.c_str(), iPort, iThreadCount);
            // LOG_INFO("server.logon", "-------------------jdata:[%s]",
            // jdata.c_str()); std::cout <<
            // "=======OnTCPNetworkSubCSImage======end===" << jdata << "
            // totalSize:" << this->data
            //           << std::endl;
            // sWarn warn;
            // try {
            //     xpack::json::decode(jdata, warn);
            //     std::cout <<
            //     "=======OnTCPNetworkSubCSImage======1111111===algid:" <<
            //     warn.algid
            //               << " cameraid : " << warn.cameraid << " level : "
            //               << warn.level
            //               << " m_nTime : " << warn.m_nTime << std::endl;
            // } catch (const std::exception &e) {
            //     std::cerr << e.what() << '\n';
            // }

            // this->data = 0;
            // memset(this->img, 0, sizeof(this->img));
        }
    }

    return true;
}

bool CAttemperEngineSink::OnTCPNetworkSubMBLogonVisitor(void *pData, uint16 wDataSize, uint32 dwSocketID)
{

    std::cout << "OnTCPNetworkSubMBLogonVisitor" << std::endl;

    uint8 sendData[1024] = {0};
    // TCP_Head *pHead = (TCP_Head *)sendData;
    // pHead->CommandInfo.wMainCmdID = MDM_MB_LOGON;        // ��Э��ID
    // pHead->CommandInfo.wSubCmdID = SUB_MB_LOGON_VISITOR; // ��Э��ID
    // pHead->TCPInfo.cbCheckCode = 0;
    // pHead->TCPInfo.cbDataKind = DK_MAPPED;
    CameraInfolist cameraList;
    xpack::json::decode(std::string((char *)pData, wDataSize), cameraList);
    for (auto &cameraInfo : cameraList.CameraInfoList)
    {
        std::cout << "ID: " << cameraInfo.ID << ", RTSP: " << cameraInfo.RTSP << ", IP: " << cameraInfo.IP << std::endl;
    }

    std::cout << "CameraInfolist size: " << cameraList.CameraInfoList.size() << std::endl;

    CameraInfolist test;
    for (int i = 0; i < 32; i++)
    {
        CameraInfo kk;
        kk.ID = i;
        kk.RTSP = "rtsp://admin:twdz321.@*************:554/hevc/ch1/main/av_stream";
        kk.IP = "*************:554";
        test.CameraInfoList.push_back(kk);
    }

    std::string cameraconfig = xpack::json::encode(test);

    // ����?��ͷ��ʾ��ֵ��

    // ���?

    // uint16 dataSize = sizeof(pHead) + sizeof(CameraInfo);
    // pHead->TCPInfo.wPacketSize = dataSize;

    // ����
    // EncryptBuffer(sendData, dataSize, 0);
    // send((char*)sendData, dataSize);
    std::cout << "send size: " << cameraconfig.size() << std::endl;
    m_pITCPNetworkEngine->SendData(dwSocketID, MDM_CS_CAMERA, SUB_CS_CAMERA_INFO, (void *)cameraconfig.data(), cameraconfig.size());
    //    sizeof(CMD_CS_CameraInfo));
    // m_pITCPNetworkEngine->SendData(dwSocketID, MDM_MB_LOGON,
    // SUB_MB_LOGON_SUCCESS, &info,
    //                                sizeof(CMD_CS_CameraInfo));

    // Ч�����?
    //  assert(wDataSize >= sizeof(CMD_MB_LogonVisitor));
    //  if (wDataSize < sizeof(CMD_MB_LogonVisitor))return false;

    // //��������
    // tagBindParameter * pBindParameter = (m_pBindParameter + dwSocketID);

    // //������Ϣ
    // CMD_MB_LogonVisitor * pLogonVisitor = (CMD_MB_LogonVisitor *)pData;

    // //��������
    // pBindParameter->cbClientKind = LinkType::LT_MOBILE;

    // LogonErrorCode eLogonErrorCode = LEC_NONE;
    // uint8 * pClientAddr = (uint8 *)&pBindParameter->dwClientAddr;
    // std::string strClientIP = StringFormat("%d.%d.%d.%d", pClientAddr[3],
    // pClientAddr[2], pClientAddr[1], pClientAddr[0]);

    // ////////////////////////////////////
    // std::string strMachine =
    // StringUtility::WStringToString((wchar_t*)pLogonVisitor->szMachineID);
    // PreparedStatement *stmt =
    // LogonDatabasePool.GetPreparedStatement(LOGON_SEL_LIMIT_ADDRESS);
    // stmt->SetString(0, strClientIP);
    // stmt->SetString(1, strMachine);
    // PreparedQueryResult result = LogonDatabasePool.Query(stmt);

    // if (result)
    // {
    // 	Field* field = result->Fetch();
    // 	while (field[3].GetInt8() == OPEN_SWITCH)
    // 	{
    // 		if (field[2].GetUInt32() < time(0))
    // 		{
    // 			//���½�ֹ��Ϣ
    // 			stmt =
    // LogonDatabasePool.GetPreparedStatement(LOGON_UPD_LIMIT_ADDRESS);
    // 			stmt->SetInt8(0, 0);
    // 			stmt->SetInt8(1, 0);
    // 			stmt->SetInt8(2, 0);
    // 			stmt->SetString(3, strClientIP);
    // 			stmt->SetString(4, strMachine);
    // 			LogonDatabasePool.DirectExecute(stmt);
    // 			break;
    // 		}

    // 		if (field[0].GetInt8() == OPEN_SWITCH)
    // 		{
    // 			eLogonErrorCode = LEC_LIMIT_IP;
    // 			break;
    // 		}

    // 		if (field[1].GetInt8() == OPEN_SWITCH)
    // 		{
    // 			eLogonErrorCode = LEC_LIMIT_MAC;
    // 			break;
    // 		}

    // 		LOG_ERROR("server.logon", "��ֹ��¼�߼����� IP: %s  MAC: %s",
    // strClientIP.c_str(), strMachine.c_str()); 		break;
    // 	}
    // }

    // //�Ƿ��ֹ���?
    // LOGON_FAILURE(dwSocketID, eLogonErrorCode)

    // //��ѯ�û���Ϣ
    // stmt = LogonDatabasePool.GetPreparedStatement(LOGON_SEL_VISITOR_ACCOUNT);
    // stmt->SetString(0, strMachine);
    // result = LogonDatabasePool.Query(stmt);
    // if (!result)
    // {
    // 	int game_id = 0;
    // 	stmt = LogonDatabasePool.GetPreparedStatement(LOGON_SEL_GAME_ID);
    // 	PreparedQueryResult result_id = LogonDatabasePool.Query(stmt);
    // 	if (result_id)
    // 	{
    // 		Field* field = result_id->Fetch();
    // 		game_id = field[0].GetInt32();

    // 		//���±�ʶ
    // 		stmt = LogonDatabasePool.GetPreparedStatement(LOGON_UPD_GAME_ID);
    // 		LogonDatabasePool.DirectExecute(stmt);
    // 	}
    // 	else
    // 	{
    // 		LOG_ERROR("server.logon", "�����ο�ID���� IP: %s  MAC: %s",
    // strClientIP.c_str(), strMachine.c_str());
    // 	}

    // 	//�����ο��û�
    // 	std::string strVisitor = StringFormat("Visitor%d", game_id);
    // 	stmt =
    // LogonDatabasePool.GetPreparedStatement(LOGON_INS_VISITOR_ACCOUNT);

    // 	std::string strUTF8Visitor;
    // 	Util::StringUtility::ConsoleToUtf8(strVisitor, strUTF8Visitor);
    // 	stmt->SetString(0, strUTF8Visitor);
    // 	stmt->SetString(1, strUTF8Visitor);
    // 	stmt->SetString(2, "");
    // 	stmt->SetString(3, "1");
    // 	stmt->SetInt8(4, 100/*pBindParameter->cbClientKind*/);
    // 	stmt->SetString(5, strClientIP);
    // 	stmt->SetString(6, strMachine);
    // 	LogonDatabasePool.DirectExecute(stmt);

    // 	//���²�ѯ�ο�
    // 	stmt =
    // LogonDatabasePool.GetPreparedStatement(LOGON_SEL_VISITOR_ACCOUNT);
    // 	stmt->SetString(0, strMachine);
    // 	result = LogonDatabasePool.Query(stmt);
    // 	if (!result)
    // 	{
    // 		LOG_ERROR("server.logon", "Insert ID IP: %s  MAC: %s",
    // strClientIP.c_str(), strMachine.c_str()); 		return false;
    // 	}
    // }

    // //��ȡ��Ϸ��Ϣ
    // Field* field = result->Fetch();
    // int id = field[0].GetInt32();
    // std::string account = field[1].GetString();
    // std::string username = field[2].GetString();
    // std::string sha_pass_hash = field[3].GetString();
    // std::string face_url = field[4].GetString();
    // int limit = field[5].GetInt8();

    // //�˺Ŷ���״̬
    // if ((limit & LEC_LIMIT_FREEZE) > 0)
    // {
    // 	eLogonErrorCode = LEC_LIMIT_FREEZE;
    // }
    // LOGON_FAILURE(dwSocketID, eLogonErrorCode)

    // //���µ�½��Ϣ
    // stmt = LogonDatabasePool.GetPreparedStatement(LOGON_UPD_VISITOR_ACCOUNT);
    // stmt->SetString(0, strClientIP);
    // stmt->SetString(1, strMachine);
    // LogonDatabasePool.DirectExecute(stmt);

    // //////////////////////////////////////////////////////////////////////////////////////////
    // CMD_MB_LogonSuccess LogonSuccess;
    // memset(&LogonSuccess, 0, sizeof(LogonSuccess));

    // LogonSuccess.dwUserID = id;
    // LogonSuccess.dwGameID = id;
    // LogonSuccess.lUserScore = field[6].GetUInt64();

    // std::string strAnsiAccount;
    // Util::StringUtility::Utf8ToConsole(account, strAnsiAccount);
    // std::wstring wstrAccount =
    // Util::StringUtility::StringToWString(strAnsiAccount);
    // swprintf((wchar_t*)LogonSuccess.szAccounts,
    // sizeof(LogonSuccess.szAccounts), L"%ls", wstrAccount.c_str());

    // std::string strAnsiUsername;
    // Util::StringUtility::Utf8ToConsole(account, strAnsiUsername);
    // std::wstring wstrUsername =
    // Util::StringUtility::StringToWString(strAnsiUsername);
    // swprintf((wchar_t*)LogonSuccess.szNickName,
    // sizeof(LogonSuccess.szNickName), L"%ls", wstrAccount.c_str());

    // std::string strAnsiSHAPass;
    // Util::StringUtility::Utf8ToConsole(sha_pass_hash, strAnsiSHAPass);
    // std::wstring wstrSHAPass =
    // Util::StringUtility::StringToWString(sha_pass_hash);
    // swprintf((wchar_t*)LogonSuccess.szDynamicPass,
    // sizeof(LogonSuccess.szDynamicPass), L"%ls", wstrSHAPass.c_str());

    // m_pITCPNetworkEngine->SendData(dwSocketID, MDM_MB_LOGON,
    // SUB_MB_LOGON_SUCCESS, &LogonSuccess, sizeof(LogonSuccess));

    // //SendKindListInfo(dwSocketID);
    // SendRoomListInfo(dwSocketID, INVALID_WORD);
    // m_pITCPNetworkEngine->SendData(dwSocketID, MDM_MB_SERVER_LIST,
    // SUB_MB_LIST_FINISH);
    return true;
}

void CAttemperEngineSink::SendKindListInfo(uint32 dwSocketID)
{
    // ��������
    uint16 wSendSize = 0;
    uint8 cbDataBuffer[SOCKET_TCP_PACKET];

    // ��������
    KindItemMap kim = m_RoomListManager.TraverseKindList();
    for (KIM_IT it = kim.begin(); it != kim.end(); ++it)
    {
        // ��������
        if ((wSendSize + sizeof(CMD_MB_GameKindItem)) > sizeof(cbDataBuffer))
        {
            m_pITCPNetworkEngine->SendData(dwSocketID, MDM_MB_SERVER_LIST, SUB_MB_KIND_LIST, cbDataBuffer, wSendSize);
            wSendSize = 0;
        }

        CMD_MB_GameKindItem GameKindItem = {};
        GameKindItem.wSortID = it->second->wSortID;
        GameKindItem.wKindID = it->second->wKindID;
        GameKindItem.wGameID = it->second->wGameID;

        GameKindItem.dwOnLineCount = it->second->dwOnLineCount;
        GameKindItem.dwAndroidCount = it->second->dwAndroidCount;
        GameKindItem.dwFullCount = it->second->dwFullCount;

        //////////////////////////////////////
        memcpy(cbDataBuffer + wSendSize, &GameKindItem, sizeof(GameKindItem));
        wSendSize += sizeof(GameKindItem);
    }

    // ����ʣ��
    if (wSendSize > 0)
        m_pITCPNetworkEngine->SendData(dwSocketID, MDM_MB_SERVER_LIST, SUB_MB_KIND_LIST, cbDataBuffer, wSendSize);
    return;
}

void CAttemperEngineSink::SendRoomListInfo(uint32 dwSocketID, uint16 wKindID)
{
    // ��������
    uint16 wSendSize = 0;
    uint8 cbDataBuffer[SOCKET_TCP_PACKET];

    // ��������
    RoomItemMap rim = m_RoomListManager.TraverseRoomList();
    for (RIM_IT it = rim.begin(); it != rim.end(); ++it)
    {
        // ��������
        if ((wSendSize + sizeof(CMD_MB_GameRoomItem)) > sizeof(cbDataBuffer))
        {
            m_pITCPNetworkEngine->SendData(dwSocketID, MDM_MB_SERVER_LIST, SUB_MB_ROOM_LIST, cbDataBuffer, wSendSize);
            wSendSize = 0;
        }

        CMD_MB_GameRoomItem GameRoomItem = {};
        GameRoomItem.wKindID = it->second->wKindID;
        GameRoomItem.wSortID = it->second->wSortID;
        GameRoomItem.wServerID = it->second->wServerID;
        GameRoomItem.wServerKind = it->second->wServerKind;
        GameRoomItem.wServerLevel = it->second->wServerLevel;
        GameRoomItem.wServerPort = it->second->wServerPort;
        GameRoomItem.wTableCount = (uint8)it->second->wTableCount;
        GameRoomItem.lCellScore = it->second->lCellScore;
        GameRoomItem.lEnterScore = it->second->lEnterScore;

        GameRoomItem.dwServerRule = it->second->dwServerRule;

        GameRoomItem.dwOnLineCount = it->second->dwOnLineCount;
        GameRoomItem.dwAndroidCount = it->second->dwAndroidCount;
        GameRoomItem.dwFullCount = it->second->dwFullCount;

        std::wstring wstrServerAddr = Util::StringUtility::StringToWString(it->second->szServerName);
        swprintf((wchar_t *)GameRoomItem.szServerAddr, sizeof(GameRoomItem.szServerAddr), L"%ls", wstrServerAddr.c_str());

        std::wstring wstrServerName = Util::StringUtility::StringToWString(it->second->szServerAddr);
        swprintf((wchar_t *)GameRoomItem.szServerName, sizeof(GameRoomItem.szServerName), L"%ls", wstrServerName.c_str());

        if (wKindID == INVALID_WORD || wKindID == it->second->wKindID)
        {
            memcpy(cbDataBuffer + wSendSize, &GameRoomItem, sizeof(GameRoomItem));
            wSendSize += sizeof(GameRoomItem);
        }
    }

    if (wSendSize > 0)
        m_pITCPNetworkEngine->SendData(dwSocketID, MDM_MB_SERVER_LIST, SUB_MB_ROOM_LIST, cbDataBuffer, wSendSize);
}

bool CAttemperEngineSink::OnLogonFailure(uint32 dwSocketID, LogonErrorCode &lec)
{
    if (lec == LEC_NONE)
    {
        return false;
    }

    CMD_MB_LogonFailure LogonFailure;
    memset(&LogonFailure, 0, sizeof(LogonFailure));

    LogonFailure.lResultCode = lec;
    std::wstring wstrLogonError = Util::StringUtility::StringToWString(LogonError[lec]);
    swprintf((wchar_t *)LogonFailure.szDescribe, sizeof(LogonFailure.szDescribe), L"%ls", wstrLogonError.c_str());

    return m_pITCPNetworkEngine->SendData(dwSocketID, MDM_MB_LOGON, SUB_MB_LOGON_FAILURE, &LogonFailure, sizeof(LogonFailure));
}
} // namespace AiBox