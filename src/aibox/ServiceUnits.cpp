#include "ServiceUnits.h"
#include "INIReader.h"
#include "Log.h"
#include <iostream>

namespace AiBox {
using namespace LogComm;
using namespace Util;
ServiceUnits *ServiceUnits::GetInstance() {
    static ServiceUnits *_instance = nullptr;
    if (_instance == nullptr) {
        _instance = new ServiceUnits();
    }
    return _instance;
}

ServiceUnits::ServiceUnits()
    : m_ServiceStatus(ServiceStatus_Stop), m_ioContext(1), m_pStrand(new Net::Strand(m_ioContext)), m_pThread(nullptr) {
}

bool ServiceUnits::Start(Net::IOContext *ioContext) {
    assert(m_ServiceStatus == ServiceStatus_Stop);
    if (m_ServiceStatus != ServiceStatus_Stop)
        return false;

    m_ServiceStatus = ServiceStatus_Config;

    if (m_pThread = new std::thread(&ServiceUnits::Run, this), m_pThread == nullptr) {
        assert(nullptr);
        return false;
    }

    if (!InitializeService()) {
        Conclude();
        return false;
    }

    // �����ں�
    if (!StartKernelService(ioContext)) {
        Conclude();
        return false;
    }

    // ���ú���
    m_funcStartNetService = [this, ioContext]() {
        if (!m_TCPNetworkEngine->Start(ioContext)) {
            return false;
        }
        return true;
    };

    SendControlPacket(SUC_LOAD_DB_GAME_LIST, NULL, 0);

    if (m_ServiceStatus != ServiceStatus_Run) {
        // ��������
        if (!m_funcStartNetService()) {
            Conclude();
            return 0;
        }

        // ����״̬
        SetServiceStatus(ServiceStatus_Run);
    }
    return true;
}
bool ServiceUnits::Conclude() {
    m_ServiceStatus = ServiceStatus_Stop;

    if (m_TimerEngine.GetDLLInterface()) {
        m_TimerEngine->Stop();
    }

    if (m_AttemperEngine.GetDLLInterface()) {
        m_AttemperEngine->Stop();
    }

    if (m_TCPNetworkEngine.GetDLLInterface()) {
        m_TCPNetworkEngine->Stop();
    }

    if (m_TCPSocketService.GetDLLInterface()) {
        m_TCPSocketService->Stop();
    }
    return true;
}

void ServiceUnits::Run() {
    // �¼�֪ͨ
    asio::io_context::work work(m_ioContext);
    m_ioContext.run();
}

bool ServiceUnits::InitializeService() {
    if ((m_TimerEngine.GetDLLInterface() == nullptr) && (!m_TimerEngine.CreateInstance())) {
        return false;
    }

    if ((m_AttemperEngine.GetDLLInterface() == nullptr) && (!m_AttemperEngine.CreateInstance())) {
        return false;
    }

    if ((m_TCPNetworkEngine.GetDLLInterface() == nullptr) && (!m_TCPNetworkEngine.CreateInstance())) {
        return false;
    }

    if ((m_TCPSocketService.GetDLLInterface() == nullptr) && (!m_TCPSocketService.CreateInstance())) {
        return false;
    }

    // ����ӿ�
    IUnknownEx *pIAttemperEngine = m_AttemperEngine.GetDLLInterface();
    IUnknownEx *pITCPNetworkEngine = m_TCPNetworkEngine.GetDLLInterface();
    IUnknownEx *pIAttemperEngineSink = QUERY_OBJECT_INTERFACE(m_AttemperEngineSink, IUnknownEx);

    // �ں����
    if (m_TimerEngine->SetTimerEngineEvent(pIAttemperEngine) == false)
        return false;
    if (m_TCPNetworkEngine->SetTCPNetworkEngineEvent(pIAttemperEngine) == false)
        return false;
    if (m_AttemperEngine->SetNetworkEngine(pITCPNetworkEngine) == false)
        return false;
    if (m_AttemperEngine->SetAttemperEngineSink(pIAttemperEngineSink) == false)
        return false;

    if (m_TCPSocketService->SetServiceID(NETWORK_CORRESPOND) == false)
        return false;
    if (m_TCPSocketService->SetTCPSocketEvent(pIAttemperEngine) == false)
        return false;

    m_AttemperEngineSink.m_pITimerEngine = m_TimerEngine.GetDLLInterface();
    m_AttemperEngineSink.m_pITCPNetworkEngine = m_TCPNetworkEngine.GetDLLInterface();
    m_AttemperEngineSink.m_pITCPSocketService = m_TCPSocketService.GetDLLInterface();

    std::string strBindIP;
#if LENDY_PLATFORM == LENDY_PLATFORM_WINDOWS
    strBindIP = sConfigMgr->Get("LocalNet", "WinBindIP", "127.0.0.1");
#else
    strBindIP = sConfigMgr->Get("LocalNet", "LinuxBindIP", "127.0.0.1");
#endif
    int iPort = sConfigMgr->GetInt32("LocalNet", "Port", 8600);
    int iThreadCount = sConfigMgr->GetInt32("LocalNet", "Threads", 4);

    if (!m_TCPNetworkEngine->SetServiceParameter(strBindIP, iPort, iThreadCount)) {
        return false;
    }
    LOG_INFO("server.logon", "Host:[%s] Port:[%d] ThreadCount:[%d]", strBindIP.c_str(), iPort, iThreadCount);
    return true;
}
bool ServiceUnits::StartKernelService(Net::IOContext *ioContext) {
    // ʱ������
    if (!m_TimerEngine->Start(ioContext)) {
        return false;
    }

    if (!m_AttemperEngine->Start(ioContext)) {
        return false;
    }

    if (!m_TCPSocketService->Start(ioContext)) {
        return false;
    }

    // ��ȡDB��DB�����������ᶨ��ܶ������ֱ���ûص�������
    LogonDatabasePool.Start(LogonDatabasePool);

    return true;
}

bool ServiceUnits::SetServiceStatus(enServiceStatus ServiceStatus) {
    if (m_ServiceStatus != ServiceStatus) {
        // ����֪ͨ
        if ((m_ServiceStatus != ServiceStatus_Run) && (ServiceStatus == ServiceStatus_Stop)) {
            LOG_INFO("server.logon", "Service failed to start");
        }

        // ���ñ���
        m_ServiceStatus = ServiceStatus;

        switch (m_ServiceStatus) {
        case ServiceStatus_Stop: // ֹͣ״̬
        {
            LOG_INFO("server.logon", "Service stopped successfully");
            break;
        }
        case ServiceStatus_Config: // ����״̬
        {
            LOG_INFO("server.logon", "Initializing component...");
            break;
        }
        case ServiceStatus_Run: // ����״̬
        {
            LOG_INFO("server.logon", "Service started successfully");
            break;
        }
        }
    }
    return true;
}

bool ServiceUnits::SendControlPacket(uint16 wControlID, void *pData, uint16 wDataSize) {
    // ״̬Ч��
    assert(m_AttemperEngine.GetDLLInterface() != nullptr);
    if (m_AttemperEngine.GetDLLInterface() == nullptr)
        return false;

    // ���Ϳ���
    return m_AttemperEngine->OnEventControl(wControlID, pData, wDataSize);
}



bool ServiceUnits::PostControlRequest(uint16 wIdentifier, void *pData, uint16 wDataSize) {
    {
        std::lock_guard<std::mutex> _lock(m_mutex);
        m_dataQueue.InsertData(wIdentifier, pData, wDataSize);
    }

    Net::post(m_ioContext,
              Net::bind_executor(*m_pStrand, [this, wIdentifier, pData, wDataSize]() { OnUIControlRequest(); }));
    return true;
}

bool ServiceUnits::OnUIControlRequest() {
    tagDataHead DataHead;
    uint8 cbBuffer[SOCKET_TCP_BUFFER] = {};

    // ��ȡ����
    std::lock_guard<std::mutex> _lock(m_mutex);
    if (m_dataQueue.DistillData(DataHead, cbBuffer, sizeof(cbBuffer)) == false) {
        assert(nullptr);
        return false;
    }

    // ���ݴ���
    switch (DataHead.wIdentifier) {
    case UDC_LOAD_DB_LIST_RESULT: // �б����
    {
        // Ч����Ϣ
        assert(DataHead.wDataSize == sizeof(ControlResult));
        if (DataHead.wDataSize != sizeof(ControlResult))
            return 0;

        // ��������
        ControlResult *pControlResult = (ControlResult *)cbBuffer;

        // ʧ�ܴ���
        if ((m_ServiceStatus != ServiceStatus_Run) && (pControlResult->cbSuccess == 0)) {
            Conclude();
            return 0;
        }

        // �ɹ�����
        if ((m_ServiceStatus != ServiceStatus_Run) && (pControlResult->cbSuccess == 1)) {
            // ����Э��
            SendControlPacket(SUC_CONNECT_CORRESPOND, NULL, 0);
        }
        return 0;
    }
    case UDC_CORRESPOND_RESULT: {
        // Ч����Ϣ
        assert(DataHead.wDataSize == sizeof(ControlResult));
        if (DataHead.wDataSize != sizeof(ControlResult))
            return 0;

        // ��������
        ControlResult *pControlResult = (ControlResult *)cbBuffer;

        // ʧ�ܴ���
        if ((m_ServiceStatus != ServiceStatus_Run) && (pControlResult->cbSuccess == 0)) {
            Conclude();
            return 0;
        }

        // �ɹ�����
        if ((m_ServiceStatus != ServiceStatus_Run) && (pControlResult->cbSuccess == 1)) {
            // ��������
            if (!m_funcStartNetService()) {
                Conclude();
                return 0;
            }

            // ����״̬
            SetServiceStatus(ServiceStatus_Run);
        }

        return 0;
    }
    case UDC_MDM_MB_CAMERA: {
        // Ч����Ϣ

        std::cout << "-----------------UDC_MDM_MB_CAMERA--------------" << std::endl;
        SendControlPacket(SUC_MDM_MB_CAMERA, NULL, 0);
        return 0;
    }

    case UDC_MDM_MB_SDEEPIMGLIB:
    {
        LOG_INFO("server.logon", "UDC_MDM_MB_SDEEPIMGLIB data:[%s] [%d]", cbBuffer, DataHead.wDataSize);
        SendControlPacket(SUC_MDM_MB_SDEEPIMGLIB, cbBuffer, DataHead.wDataSize);
        return 0;
    }
    }

    return true;
}
} // namespace AiBox