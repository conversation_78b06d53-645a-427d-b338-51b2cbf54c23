# -*- coding: utf-8 -*-
import os
import re
from datetime import datetime, timed<PERSON><PERSON>

def delete_old_logs(log_dir, days_threshold=7):

    pattern = re.compile(r'AI_Box_(\d{4}-\d{2}-\d{2})_\d{2}-\d{2}-\d{2}\.log')
    cutoff_date = datetime.now() - timedelta(days=days_threshold)
    
    deleted_files = []
    for filename in os.listdir(log_dir):
        match = pattern.match(filename)
        if match:
            file_date = datetime.strptime(match.group(1), "%Y-%m-%d")
            if file_date < cutoff_date:
                filepath = os.path.join(log_dir, filename)
                try:
                    os.remove(filepath)
                    deleted_files.append(filename)
                except Exception as e:
                   print("remove file {}: {}".format(filename, str(e)))

    
    print("remove file cnt: {}".format(len(deleted_files)))
    return deleted_files

if __name__ == "__main__":
    # 使用示例：修改为你的日志目录路径
    log_directory = "/home/<USER>/aibox_server/EnergyStorage/AI_Box/bin/log"
    delete_old_logs(log_directory,10)

