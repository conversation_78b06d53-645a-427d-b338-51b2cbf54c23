
[Unit]
Description=AI Box Service
After=network.target

[Service]
Type=simple
User=root
Group=root
WorkingDirectory=/home/<USER>/EnergyStorage/AI_Box/bin/
ExecStart=/home/<USER>/EnergyStorage/AI_Box/bin/start.sh
Restart=on-failure
RestartSec=5s
StartLimitBurst=5
MemoryLimit=2G
CPUQuota=200%
LimitNOFILE=65536
Environment="PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin"
StandardOutput=syslog
StandardError=syslog
SyslogIdentifier=aibox

[Install]
WantedBy=multi-user.target
